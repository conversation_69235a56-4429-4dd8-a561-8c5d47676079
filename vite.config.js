import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { VitePWA } from 'vite-plugin-pwa'

console.log('--- Loading simplified vite.config.js ---'); // Add a debug log

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
      vue(),
      VitePWA({
        registerType: 'autoUpdate',
        devOptions: {
          enabled: false
        },
        workbox: {
          globPatterns: ['**/*.{js,css,ico,png,svg}']
        },
        includeAssets: ['img/favicon.ico', 'img/icon_180.png', 'img/masked-icon.svg'],
        manifest: {
          name: 'soga导航',
          short_name: 'soga',
          description: 'soga导航，实时搜索，导航',
          theme_color: '#ffffff',
          icons: [
            {
              src: 'img/icon_192.png',
              sizes: '192x192',
              type: 'image/png'
            },
            {
              src: 'img/icon_512.png',
              sizes: '512x512',
              type: 'image/png'
            }
          ]
        }
      })
  ],
  build: {
    outDir: './dist',
    emptyOutDir: true
  }

})
