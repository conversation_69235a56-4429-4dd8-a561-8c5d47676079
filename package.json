{"name": "soga-ui", "version": "0.0.0", "scripts": {"dev": "vite --host 0.0.0.0", "build": "vite build", "serve": "vite preview"}, "dependencies": {"@vueuse/head": "^1.1.26", "axios": "^1.8.2", "highlight.js": "^11.11.1", "markdown-it": "^14.1.0", "pinyin": "^4.0.0", "slugify": "^1.6.6", "vue": "^3.2.16", "vue-draggable-next": "^2.2.1", "vue-router": "^4.1.3", "vuex": "^4.0.2"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.7", "@tailwindcss/typography": "^0.5.16", "@vitejs/plugin-vue": "^5.0.0", "autoprefixer": "^10.4.21", "postcss": "^8.5.3", "tailwindcss": "^4.1.7", "vite": "^5.0.0", "vite-plugin-pwa": "^0.17.0"}}