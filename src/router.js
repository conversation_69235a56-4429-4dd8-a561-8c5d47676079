import { createRouter, createWebHistory } from 'vue-router'
import SearchPage from './pages/SearchPage.vue'
import GithubPage from './pages/GithubPage.vue'
import GithubArticles from './pages/News.vue'
import NotFound from "./pages/NotFound.vue"
import Home from "./pages/Home.vue"
import NavPage from "./pages/NavPage.vue"
import NotePage from "./pages/NotePage.vue"
import StockPage from './pages/stock/StockPage.vue'
import NotAvailable from "./pages/NotAvailable.vue"


// 2. Define some routes
// Each route should map to a component.
// We'll talk about nested routes later.
const routes = [
    {
        name: 'home',  path: '/', component: Home, meta: { spacing: false }
    },
    {
        name: 'search', path: '/search', component: SearchPage
    },
    {
        name: 'news', path: '/news', component: GithubArticles
    },
    {
        name: 'github', path: '/:repo(a|m)/:key(.+)?', component: GithubPage
    },
    {
        name: 'nav', path: '/nav', component: NavPage, meta: { spacing: false }
    },
    {
        name: 'note', path: '/note', component: NotePage
    },
    {
        name: 'stock', path: '/stock', component: StockPage
    },
    {
        name: 'notFound',
        path: '/:pathMatch(.*)*',
        component: NotFound
    },
    {
        name: 'notAvailable',
        path: '/not-available',
        component: NotAvailable
    }
]

// 3. Create the router instance and pass the `routes` option
// You can pass in additional options here, but let's
// keep it simple for now.
export default createRouter({
    // 4. Provide the history implementation to use. We are using the hash history for simplicity here.
    history: createWebHistory(),
    routes, // short for `routes: routes`
    scrollBehavior(to, from, savedPosition) {
        if (savedPosition) {
            return savedPosition;
        }

        if (to.hash) {
            // 使用 Promise + setTimeout 确保 DOM 更新完成
            return new Promise((resolve) => {
                setTimeout(() => { // 延迟确保元素渲染
                    let element = null;
                    const hashValue = to.hash.substring(1); // 获取 hash 值，去掉 '#'

                    if (hashValue) {
                        // 尝试 1: 直接使用 getElementById (对数字开头的 ID 有效)
                        element = document.getElementById(hashValue);

                        // 尝试 2: 如果找不到，尝试 GitHub 风格的 'user-content-' 前缀
                        if (!element) {
                            try {
                                const decodedHash = decodeURIComponent(hashValue);
                                element = document.getElementById('user-content-' + decodedHash);
                            } catch(e) {
                                console.error("Error decoding hash for user-content lookup:", e);
                            }
                        }
                    }


                    if (element) {
                        const header = document.querySelector('.sticky-header');
                        const headerHeight = header ? header.offsetHeight : 0;
                        const offset = 10; 

                        // 使用 getBoundingClientRect 重新计算 top
                        const elementRect = element.getBoundingClientRect();
                        const absoluteElementTop = elementRect.top + window.scrollY;
                        const top = absoluteElementTop - headerHeight - offset; // 计算滚动目标

                        resolve({ top, behavior: 'smooth' });
                    } else {
                        console.warn(`Element not found for hash: ${to.hash}`);
                        resolve({ top: 0 }); // 找不到元素，滚动到顶部
                    }
                }, 100); // 100ms 延迟
            });
        }

        // 默认滚动到页面顶部
        // document.getElementById("app")?.scrollIntoView({ behavior: "smooth" }); // 可能导致不期望的滚动，改为返回 top: 0
        return { top: 0, behavior: 'smooth' };
    },
})

