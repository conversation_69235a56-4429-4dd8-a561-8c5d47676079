<script setup>
  import {ref, onMounted} from "vue";
  import LinkDialog from "../components/LinkDialog.vue";
  import { useHead } from '@vueuse/head';

  const year = ref(new Date().getFullYear());
  let submitDialog = ref(false);

  // SEO 优化
  useHead({
    meta: [
      { name: 'description', content: '889990.xyz - 实时网址导航平台，提供热门资讯、隐私保护和个性化定制服务，守护您的数字隐私安全。' },
      { name: 'keywords', content: '实时网址导航,数字隐私,热门资讯,隐私保护,个性化定制,网站收录' },
      // 添加结构化数据
      { property: 'og:type', content: 'website' },
      { property: 'og:site_name', content: '889990.xyz 实时网址导航' }
    ]
  });

  // 结构化数据
  const structuredData = {
    '@context': 'https://schema.org',
    '@type': 'WebSite',
    'name': '889990.xyz 实时网址导航',
    'description': '实时网址导航平台，提供热门资讯、隐私保护和个性化定制服务，守护您的数字隐私安全。',
    'url': 'https://889990.xyz',
    'potentialAction': {
      '@type': 'SearchAction',
      'target': 'https://889990.xyz/search?q={search_term_string}',
      'query-input': 'required name=search_term_string'
    }
  };

  // 添加结构化数据到页面
  onMounted(() => {
    const script = document.createElement('script');
    script.type = 'application/ld+json';
    script.textContent = JSON.stringify(structuredData);
    document.head.appendChild(script);
  });

  const openDialog = () => {
    submitDialog.value = true;
  }

  const closeDialog = () => {
    submitDialog.value = false;
  }
</script>

<template>
 <footer class="flex flex-col justify-center items-center text-xs text-gray-400 mt-12 mb-8 text-center md:flex-row">
   <div>889990.xyz 实时网址导航&nbsp;</div>
   <div> ©{{year}} |
     <a href="/#news" title="浏览热门实时资讯">实时资讯</a> |
     <a href="/#privacy" title="查看我们的隐私承诺">隐私承诺</a> |
     <a href="/#customize" title="体验个性化定制服务">个性化定制</a> |
     <span v-on:click="openDialog" class="link-dialog-text" title="申请网站收录">申请收录</span>
   </div>

   <LinkDialog :visible="submitDialog"
               @close="closeDialog"></LinkDialog>
 </footer>
</template>

<style scoped>
  .footer-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 4rem;
    font-size: .75rem;
    color: #999;
    background: #fff;
  }

  .footer-container a {
    margin-left: .75rem;
    text-decoration: none;
    color: #666;
  }

  .footer-container a:hover {
    text-decoration: underline;
  }

  .link-dialog-text {
    padding-left: 2px;
    cursor: pointer;
    user-select: none;
  }

  a {
    color: inherit;
    text-decoration: none;
    transition: color 0.2s ease;
  }

  a:hover {
    color: #666;
    text-decoration: underline;
  }
</style>
