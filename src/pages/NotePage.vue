<script setup>
import { ref, computed, onMounted, inject, onUnmounted } from 'vue'
import { useStore } from 'vuex'
import BodyLayout from "../components/BodyLayout.vue"
import NoteDialog from "../components/NoteDialog.vue"
import NoteViewDialog from "../components/NoteViewDialog.vue"
import SkeletonItem from "../components/SkeletonItem.vue"
import { hexToRgbArray, rgbToHsl, hslToRgbString } from '../utils/color.js'
import useDialog from '../utils/useDialog.js'

const http = inject('http')
const store = useStore()
const { confirm, toast } = useDialog()

// 获取用户状态
const user = computed(() => store.state.user)

// 笔记相关状态
const notes = ref([])
const loading = ref(false)
const showEditDialog = ref(false)
const showViewDialog = ref(false)
const currentNote = ref(null)
const likedNotes = new Set();

// 分页相关 (这部分逻辑未改动)
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)

// 获取笔记列表 (逻辑未改动)
const fetchNotes = async (isLoadMore = false) => {
  if (loading.value) return;
  loading.value = true
  try {
    const response = await http.primary().get('/note/lists', {
      params: {
        page: currentPage.value,
        pageSize: pageSize.value
      }
    })
    const newNotes = response.data.data || [];
    if (isLoadMore) {
      notes.value.push(...newNotes);
    } else {
      notes.value = newNotes;
    }
    total.value = response.data.total || 0
  } catch (error) {
    console.error('获取笔记失败:', error)
    toast('获取笔记失败，请重试', { large: true })
  } finally {
    loading.value = false
  }
}

// 格式化时间 (逻辑未改动)
const formatTime = (timestamp) => {
  const date = new Date(timestamp)
  const now = new Date()
  const diff = now - date
  
  if (diff < 60000) return '刚刚'
  if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`
  if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`
  if (diff < 2592000000) return `${Math.floor(diff / 86400000)}天前`
  return date.toLocaleDateString()
}

// 检查是否是当前用户的笔记 (逻辑未改动)
const isMyNote = (note) => {
  return !note.userId || (user.value && String(note.userId) === String(user.value.id))
}

const getCardDynamicStyles = (hexColor) => {
  const [r, g, b] = hexToRgbArray(hexColor);
  const [h, s, l] = rgbToHsl(r, g, b);
  const isLight = l > 0.7;

  const baseRgb = `${r}, ${g}, ${b}`;
  const elementLightness = isLight ? Math.max(0, l - 0.5) : Math.min(1, l + 0.5);
  const elementRgb = hslToRgbString(h, s, elementLightness);

  return {
    '--text-color': `rgba(${elementRgb}, 0.9)`,
    '--text-color-medium': `rgba(${elementRgb}, 0.7)`,
    '--text-color-light': `rgba(${elementRgb}, 0.5)`,
    '--bg-color-light': `rgba(${elementRgb}, 0.1)`,
    'backgroundColor': `rgba(${baseRgb}, 0.6)`
  };
};

const openEditDialog = (note = null) => {
  currentNote.value = note
  showEditDialog.value = true
}

const openViewDialog = (note) => {
  currentNote.value = note
  showViewDialog.value = true
}

const closeAllDialogs = () => {
  showEditDialog.value = false
  showViewDialog.value = false
  currentNote.value = null
}

const saveNote = async (noteData) => {
  try {
    const payload = JSON.parse(JSON.stringify(noteData));
    if (currentNote.value) {
      await http.patch(`/note/modify/${currentNote.value.id}`, payload)
      toast('修改成功', { large: true })
    } else {
      await http.post('/note/save', payload)
      toast('发布成功', { large: true })
    }
    
    closeAllDialogs()
    currentPage.value = 1;
    notes.value = [];
    fetchNotes()
  } catch (error) {
    console.error('保存笔记失败:', error)
    toast('保存失败，请重试', { large: true })
  }
}

// 删除笔记 (逻辑未改动)
const deleteNote = async (noteId) => {
  const result = await confirm('确认删除', '确定要删除这条笔记吗？这就像把信丢进火里，再也找不回来了哦。')
  if (!result) return

  try {
    await http.delete(`/note/remove/${noteId}`)
    toast('删除成功', { large: true })
    currentPage.value = 1;
    notes.value = [];
    fetchNotes()
  } catch (error) {
    console.error('删除笔记失败:', error)
    toast('删除失败，请重试', { large: true })
  }
}

// 点赞笔记 (逻辑未改动)
const likeNote = async (noteId) => {
  if (likedNotes.has(noteId)) {
    toast('你已经点过赞啦', { large: true });
    return;
  }

  const note = notes.value.find(n => n.id === noteId);
  if (note) {
    note.likes++;
    likedNotes.add(noteId);
  }

  try {
    await http.post(`/note/like/${noteId}`)
    // No need to fetch notes again, we've updated it locally.
  } catch (error) {
    console.error('点赞失败:', error)
    toast('点赞失败，请重试', { large: true })
    // Rollback optimistic update
    if (note) {
      note.likes--;
      likedNotes.delete(noteId);
    }
  }
}

// 处理键盘事件 (逻辑未改动)
const handleKeydown = (event) => {
  if (event.key === 'Escape') {
    closeAllDialogs()
  }
}

const handleScroll = () => {
  const { scrollTop, scrollHeight, clientHeight } = document.documentElement;
  if (scrollTop + clientHeight >= scrollHeight - 5) {
    if (notes.value.length < total.value) {
      currentPage.value++;
      fetchNotes(true);
    }
  }
};

onMounted(() => {
  fetchNotes()
  document.addEventListener('keydown', handleKeydown)
  window.addEventListener('scroll', handleScroll);
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
  window.removeEventListener('scroll', handleScroll);
})
</script>

<template>
  <BodyLayout :spacing="false">

    <!-- **改动**: 使用更柔和的背景色，增加内边距 -->
    <div class="note-page flex-auto min-h-screen tech-background from-stone-50 to-blue-50 p-4 sm:p-6 lg:p-8">
      <div class="tech-shapes">
        <div class="bg-shape shape-a"></div>
        <div class="bg-shape shape-b"></div>
        <div class="bg-shape shape-c"></div>
      </div>
    
      <!-- **改动**: 页面标题区，增加副标题，优化按钮样式 -->
      <div class="mb-8 text-center select-none">
        <h1 class="text-4xl font-bold text-gray-800 tracking-wider">心情树洞</h1>
        <p class="mt-2 text-gray-500">把你的思绪安放在这里，让它们静静生长。</p>
      </div>

      <!-- **改动**: 将新增按钮移动到右上角固定位置，方便随时点击 -->
      <div class="fixed top-20 right-6 z-40">
        <button
          @click="openEditDialog()"
          class="add-note-btn"
          title="写新笔记"
        >
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
          </svg>
        </button>
      </div>

  

      <!-- 骨架屏加载状态 -->
      <div v-if="loading && notes.length === 0" class="note-grid">
        <div v-for="i in 6" :key="`skeleton-${i}`" class="skeleton-card">
          <SkeletonItem :rows="3" />
        </div>
      </div>

      <!-- **改动**: 笔记网格和卡片样式完全重构 -->
      <div v-else-if="notes.length > 0" class="note-grid">
        <div
          v-for="note in notes"
          :key="note.id"
          class="note-card group"
          :style="getCardDynamicStyles(note.style || '#a2e1d4')"
          @click="openViewDialog(note)"
        >
          <div class="note-header">
            <div class="flex items-center gap-1 text-xs">
              <span>{{ note.username }}</span>
              <span>·</span>
              <span>{{ formatTime(note.modifyTime) }}</span>
            </div>
          </div>
          <div class="note-content-wrapper">
            <!-- 笔记内容 -->
            <p class="note-content">{{ note.content }}</p>

            <!-- 笔记底部信息 -->
            <div class="note-footer">
              
              <div class="flex items-center gap-1">
                <!-- 标签 -->
                <div v-if="note.tags && note.tags.length" class="note-tags">
                  <span v-for="tag in note.tags" :key="tag" class="note-tag">#{{ tag }}</span>
                </div>
               
              </div>
            </div>
          </div>
          
          <!-- 操作按钮 -->
          <div class="note-actions">
            <div class="action-btn-group" v-if="isMyNote(note)">
              <button @click.stop="openEditDialog(note)" class="action-btn" title="编辑">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path></svg>
              </button>
              <button @click.stop="deleteNote(note.id)" class="action-btn" title="删除">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path></svg>
              </button>
            </div>
             <button
                  @click.stop="likeNote(note.id)"
                  class="like-btn"
                >
                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="12000" width="20" height="20">
                  <path d="M276.645453 400.987794l-184.692811 0.830875a36.103351 36.103351 0 0 0-35.958851 36.276752L57.800042 844.032343a36.125026 36.125026 0 0 0 36.283977 35.966077l184.685585-0.823651a36.125026 36.125026 0 0 0 35.966077-36.283977l-1.806252-405.936922A36.110576 36.110576 0 0 0 276.645453 400.987794zM966.510633 412.424977a36.132251 36.132251 0 0 0-29.015621-14.384985l-267.679221 1.192125 47.554984-143.141804a36.269527 36.269527 0 0 0 1.842377-11.552784c-0.24565-55.047315-45.228533-99.632823-100.275849-99.387173a99.163198 99.163198 0 0 0-70.472701 29.564722A99.127073 99.127073 0 0 0 520.200381 233.490496L400.048543 416.61548a36.060001 36.060001 0 0 0-5.924504 19.97714l1.806251 405.929697a36.132251 36.132251 0 0 0 36.283977 35.973301l387.137458-1.719551a36.103351 36.103351 0 0 0 34.51385-26.024469l118.461187-406.478798a36.052776 36.052776 0 0 0-5.816129-31.847823z" p-id="12001"></path>
                </svg>
                <span>{{ note.likes }}</span>
            </button>
          </div>
        </div>
      </div>

      <!-- **改动**: 空状态，使用SVG插画，更具吸引力 -->
      <div v-else class="text-center py-20">
        <svg class="w-40 h-40 mx-auto text-gray-300" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round">
          <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
          <polyline points="14 2 14 8 20 8"></polyline>
          <line x1="16" y1="13" x2="8" y2="13"></line>
          <line x1="16" y1="17" x2="8" y2="17"></line>
          <polyline points="10 9 9 9 8 9"></polyline>
        </svg>
        <p class="mt-6 text-xl text-gray-500">这里空空如也...</p>
        <p class="mt-2 text-gray-400">不如，写下你的第一条心情笔记？</p>
        <button @click="openEditDialog()" class="mt-6 bg-blue-500 hover:bg-blue-600 text-white font-bold py-3 px-6 rounded-full transition-all duration-300 transform hover:scale-105">
          立即书写
        </button>
      </div>
    </div>

    <!-- 新的NoteDialog组件 -->
    <NoteDialog
      :visible="showEditDialog"
      :note="currentNote"
      :user="user"
      @close="closeAllDialogs"
      @submit="saveNote"
    />
    <NoteViewDialog
      :visible="showViewDialog"
      :note="currentNote"
      @close="closeAllDialogs"
    />
    

  </BodyLayout>

</template>

<style>
</style>

<style scoped>

 /*  background: linear-gradient(135deg, #e6f7ff, #fff 66.6%, #e6f7ff 66.6%); */
.tech-background {
  background: linear-gradient(135deg, #f0f8ff, #e6f0fa);
  position: relative;
  z-index: 0;
}

.tech-shapes {
  position: fixed;
  inset: 0;
  pointer-events: none;
  z-index: -1;
}

.tech-shapes .bg-shape {
  position: absolute;
  opacity: 0.4;
  mix-blend-mode: lighten;
  box-shadow: 0 0 40px rgba(0, 136, 255, 0.2);
}

/* 大圆弧扇形，仅显露一角 */
.tech-shapes .shape-a {
  top: -50%;
  left: -60%;
  width: 180%;
  height: 180%;
  background: radial-gradient(circle at top left, #b3e5fc 0%, #e1f5fe 100%);
  border-radius: 0 100% 100% 100%;
  transform: rotate(45deg);
}

/* 椭圆片状，高扁形，右下边缘滑入 */
.tech-shapes .shape-b {
  bottom: -40%;
  right: -30%;
  width: 140%;
  height: 60%;
  background: linear-gradient(135deg, #81d4fa 0%, #d0f0ff 100%);
  border-radius: 70% 30% 60% 20% / 40% 80% 20% 60%;
  transform: rotate(-25deg);
}

/* 锋利飞翼形状，自定义渐变与剪裁 */
.tech-shapes .shape-c {
  top: -35%;
  right: -60%;
  width: 200%;
  height: 100%;
  background: conic-gradient(from 90deg at 30% 30%, #4fc3f7, #bbdefb, #4fc3f7);
  clip-path: polygon(100% 0%, 100% 50%, 0% 100%, 0% 0%);
  transform: rotate(30deg);
}

/* **改动**: 使用瀑布流布局以适应不同高度的笔记 */
.note-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1.5rem;
}

/* 骨架屏卡片样式 */
.skeleton-card {
  border-radius: 0.75rem;
  background-color: #fff;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  overflow: hidden;
}

/* **改动**: 全新的笔记卡片样式 */
.note-card {
  position: relative;
  border-radius: 0.75rem; /* 12px */
  transition: all 0.3s ease-in-out;
  display: flex;
  flex-direction: column;
  padding: 15px; 
  cursor: default;
  break-inside: avoid-column; /* For masonry-like behavior if you switch to column layout */
}
.note-card:hover {
  box-shadow: 0 10px 15px -3px rgba(0,0,0,0.1), 0 4px 6px -2px rgba(0,0,0,0.05);
}

.note-content-wrapper {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  min-height: 120px; /* 保证卡片有最小高度 */
}

.note-content {
  color: var(--text-color);
  font-size: 14px;
  line-height: 1.6;
  flex-grow: 1;
  word-wrap: break-word;
  display: -webkit-box;
  -webkit-line-clamp: 4;
  line-clamp: 4;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.note-footer {
  margin-top: 4px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: var(--text-color-medium);
}


.note-header {
  margin-bottom: 0.5rem;
  font-size: 14px;
  font-weight: bold;
  color: var(--text-color);
}

.like-btn {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.5rem;
  border-radius: 9999px;

  color: var(--text-color-medium);
  transition: all 0.2s ease;
  cursor: pointer;
}


.like-btn:hover:not(:disabled) {
  color: var(--text-color);
  transform: scale(1.1);
}

.group:hover .like-btn {
  background-color: var(--bg-color-light);
}

.note-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}
.note-tag {
  font-size: 0.75rem;
  padding: 0.1rem 0.6rem;
  border-radius: 9999px;
  background-color: var(--bg-color-light);
  color: var(--text-color-medium);
}



.note-actions {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  display: flex;
  gap: 0.25rem;
}

.note-actions .action-btn-group {
  opacity: 0;
  transform: translateY(-10px);
  transition: all 0.2s ease-in-out;
  display: inline-flex;
   gap: 0.25rem;
}

.group:hover .action-btn-group  {
  opacity: 1;
  transform: translateY(0);
}

.action-btn {
  width: 2rem;
  height: 2rem;
  border-radius: 9999px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.2s ease;
  background-color: var(--bg-color-light);
  color: var(--text-color-medium);
  cursor: pointer;
  
}
.action-btn:hover {
  color: var(--text-color);
  transform: scale(1.1);
  
}


.add-note-btn {
  background-color: white;
  color: #374151; /* gray-700 */
  border-radius: 9999px;
  width: 3.5rem;
  height: 3.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
}
.add-note-btn:hover {
  transform: scale(1.05) rotate(90deg);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
}
</style>