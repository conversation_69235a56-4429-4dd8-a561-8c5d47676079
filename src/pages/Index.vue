<script setup>
import Header from './Header.vue'
import Footer from './Footer.vue'
import FriendlyPage from './FriendlyPage.vue';

</script>

<template>
  <Header></Header>
  
  <router-view v-slot="{ Component, route }">
    <keep-alive>
      <FriendlyPage v-if="route.path" :key="route.path + '/' + (route.query.q || 'index')">
        <Component :is="Component"/>
      </FriendlyPage>
    </keep-alive>

  </router-view>
  <Footer></Footer>
</template>

<style scoped>

</style>
