<script setup>
import Header from './Header.vue'
import Footer from './Footer.vue'
import BodyLayout from '../components/BodyLayout.vue';
import { useHttp } from '../utils/http.js';
const { status } = useHttp(); 

</script>

<template>
  <Header></Header>
  
  <router-view v-slot="{ Component, route }">
    <keep-alive>
      <Component :is="Component" v-if="route.path" :key="route.path + '/' + (route.query.q || 'index')"/>
    </keep-alive>
  </router-view>
 
  <Footer></Footer>
</template>

<style scoped>

</style>
