<template>
  <div class="min-h-screen bg-gradient-to-br from-white via-blue-50 to-gray-50 flex items-center justify-center p-4 overflow-hidden relative select-none">
    <!-- 科技背景网格 -->
    <div class="absolute inset-0 opacity-5">
      <div class="absolute inset-0" style="background-image: linear-gradient(rgba(59, 130, 246, 0.3) 1px, transparent 1px), linear-gradient(90deg, rgba(59, 130, 246, 0.3) 1px, transparent 1px); background-size: 50px 50px;"></div>
    </div>

    <!-- 动态背景元素 -->
    <div class="absolute inset-0 overflow-hidden">
      <div class="absolute -top-40 -right-40 w-80 h-80 bg-blue-100/60 rounded-full filter blur-3xl animate-pulse"></div>
      <div class="absolute -bottom-40 -left-40 w-80 h-80 bg-blue-200/40 rounded-full filter blur-3xl animate-pulse delay-1000"></div>
      <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-60 h-60 bg-blue-50/80 rounded-full filter blur-2xl animate-bounce"></div>
    </div>

    <!-- 科技风格线条 -->
    <div class="absolute inset-0 pointer-events-none">
      <div class="absolute top-20 left-0 w-full h-px bg-gradient-to-r from-transparent via-blue-300/40 to-transparent animate-pulse"></div>
      <div class="absolute bottom-20 left-0 w-full h-px bg-gradient-to-r from-transparent via-blue-300/40 to-transparent animate-pulse delay-500"></div>
      <div class="absolute left-20 top-0 w-px h-full bg-gradient-to-b from-transparent via-blue-200/30 to-transparent animate-pulse delay-300"></div>
      <div class="absolute right-20 top-0 w-px h-full bg-gradient-to-b from-transparent via-blue-200/30 to-transparent animate-pulse delay-700"></div>
    </div>

    <!-- 浮动科技元素 -->
    <div class="absolute inset-0 pointer-events-none">
      <div class="absolute top-20 left-20 w-3 h-3 bg-blue-400 rounded-full animate-tech-float delay-300 shadow-lg shadow-blue-400/30"></div>
      <div class="absolute top-40 right-32 w-4 h-4 bg-blue-500 rounded-full animate-tech-float delay-700 shadow-lg shadow-blue-500/30"></div>
      <div class="absolute bottom-32 left-40 w-2 h-2 bg-blue-600 rounded-full animate-tech-float delay-1000 shadow-lg shadow-blue-600/30"></div>
      <div class="absolute bottom-20 right-20 w-3 h-3 bg-blue-400 rounded-full animate-tech-float delay-500 shadow-lg shadow-blue-400/30"></div>
      
      <!-- 科技风格方块 -->
      <div class="absolute top-1/4 left-10 w-6 h-6 border border-blue-300/60 animate-tech-rotate"></div>
      <div class="absolute top-3/4 right-10 w-4 h-4 border border-blue-400/60 animate-tech-rotate delay-500"></div>
      <div class="absolute top-1/2 left-1/4 w-5 h-5 border border-blue-500/50 animate-tech-rotate delay-1000"></div>
    </div>

    <!-- 数字雨效果 -->
    <div class="absolute inset-0 pointer-events-none opacity-15">
      <div class="absolute top-10 left-1/4 text-blue-500 text-xs font-mono animate-digital-rain">01010101</div>
      <div class="absolute top-20 right-1/3 text-blue-600 text-xs font-mono animate-digital-rain delay-500">11001100</div>
      <div class="absolute bottom-32 left-1/3 text-blue-400 text-xs font-mono animate-digital-rain delay-1000">10101010</div>
    </div>

    <!-- 主要内容 -->
    <div class="relative z-10 text-center max-w-2xl mx-auto">
      <!-- 科技风格图标区域 -->
      <div class="mb-12 relative">
        <div class="inline-flex items-center justify-center w-32 h-32 rounded-full border-2 border-blue-300/40 p-1 animate-tech-spin relative">
          <!-- 外圈装饰 -->
          <div class="absolute inset-0 rounded-full border border-blue-200/30 animate-spin-reverse"></div>
          <div class="absolute inset-2 rounded-full border border-blue-400/30 animate-spin"></div>
          
          <div class="flex items-center justify-center w-full h-full bg-white/90 rounded-full backdrop-blur-sm border border-blue-200/40 shadow-lg">
            <div class="relative">
              <!-- 主要图标 -->
              <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center transform rotate-12 animate-bounce shadow-lg shadow-blue-500/30">
                <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                </svg>
              </div>
              <!-- 状态指示器 -->
              <div class="absolute -top-2 -right-2 w-6 h-6 bg-blue-400 rounded-full flex items-center justify-center animate-ping shadow-lg shadow-blue-400/30">
                <div class="w-3 h-3 bg-blue-500 rounded-full"></div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 错误代码 -->
      <div class="mb-6">
        <h1 class="text-8xl md:text-9xl font-black bg-gradient-to-r from-blue-600 via-blue-700 to-blue-800 bg-clip-text text-transparent leading-none animate-pulse font-mono tracking-wider">
          {{ errorCode || '503' }}
        </h1>
        <div class="flex justify-center mt-2">
          <div class="h-1 w-24 bg-gradient-to-r from-transparent via-blue-500 to-transparent animate-pulse"></div>
        </div>
      </div>

      <!-- 主要错误信息 -->
      <div class="mb-8">
        <h2 class="text-2xl md:text-3xl font-bold text-blue-800 mb-4 animate-fade-in-up">
          抱歉, 服务暂不可用...
        </h2>
        <p class="text-lg text-blue-600 leading-relaxed animate-fade-in-up delay-200 font-light">
          
        </p>
      </div>

      <!-- 错误详情 -->
      <div v-if="errorMessage || errorStack" class="mb-8 space-y-4">
        <div v-if="errorMessage" class="bg-white/80 backdrop-blur-sm rounded-2xl p-6 border border-blue-200/60 shadow-lg animate-fade-in-up delay-300">
          <h3 class="text-sm font-semibold text-blue-700 mb-2 uppercase tracking-wide flex items-center">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            错误信息
          </h3>
          <p class="text-blue-800 text-left font-mono text-sm">{{ errorMessage }}</p>
        </div>
        
        <div v-if="errorStack" class="bg-white/80 backdrop-blur-sm rounded-2xl p-6 border border-blue-200/60 shadow-lg animate-fade-in-up delay-400">
          <h3 class="text-sm font-semibold text-blue-700 mb-2 uppercase tracking-wide flex items-center">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"></path>
            </svg>
            技术详情
          </h3>
          <pre class="text-xs text-blue-700 bg-blue-50/80 p-4 rounded-xl overflow-auto max-h-32 text-left font-mono whitespace-pre-wrap border border-blue-200/40">{{ errorStack }}</pre>
        </div>
      </div>

      <!-- 操作按钮组 -->
      <div class="flex flex-col sm:flex-row gap-4 justify-center items-center animate-fade-in-up delay-500">
        <button 
          @click="refresh" 
          class="group relative px-8 py-4 bg-gradient-to-r from-blue-500 to-blue-600 text-white font-semibold rounded-2xl shadow-lg shadow-blue-500/30 hover:shadow-xl hover:shadow-blue-500/40 transform hover:-translate-y-1 transition-all duration-300 ease-out focus:outline-none focus:ring-4 focus:ring-blue-400/50 border border-blue-400/30"
        >
          <span class="relative z-10 flex items-center space-x-2">
            <svg class="w-5 h-5 group-hover:rotate-180 transition-transform duration-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
            </svg>
            <span>重新加载</span>
          </span>
          <div class="absolute inset-0 bg-gradient-to-r from-blue-600 to-blue-700 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
        </button>

        <button 
          @click="goHome" 
          class="px-8 py-4 bg-white/80 backdrop-blur-sm text-blue-700 font-semibold rounded-2xl border-2 border-blue-300/60 hover:border-blue-400/80 hover:bg-white/90 transition-all duration-300 ease-out focus:outline-none focus:ring-4 focus:ring-blue-300/30 flex items-center space-x-2 shadow-lg"
        >
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
          </svg>
          <span>返回主页</span>
        </button>
      </div>

      <!-- 底部状态信息 -->
      <div class="mt-12 text-center animate-fade-in-up delay-700">

        <div class="flex justify-center space-x-8 text-xs text-blue-500">
          <span class="flex items-center space-x-2">
            <div class="w-2 h-2 bg-blue-400 rounded-full animate-pulse shadow-sm shadow-blue-400/50"></div>
            <span class="font-mono">持续监控</span>
          </span>
          <span class="flex items-center space-x-2">
            <div class="w-2 h-2 bg-blue-500 rounded-full animate-pulse delay-300 shadow-sm shadow-blue-500/50"></div>
            <span class="font-mono">故障转移</span>
          </span>
          <span class="flex items-center space-x-2">
            <div class="w-2 h-2 bg-blue-600 rounded-full animate-pulse delay-600 shadow-sm shadow-blue-600/50"></div>
            <span class="font-mono">缓存优化</span>
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { defineProps } from 'vue';
import { useRouter } from 'vue-router';

const router = useRouter();

defineProps({
  'errorCode': String,
  'errorMessage': String,
  'errorStack': String
});

const refresh = () => {
  window.location.reload();
};

const goHome = () => {
  router.push('/');
};
</script>

<style scoped>
@keyframes tech-float {
  0%, 100% { 
    transform: translateY(0px) scale(1); 
    opacity: 0.8;
  }
  50% { 
    transform: translateY(-20px) scale(1.1); 
    opacity: 1;
  }
}

@keyframes tech-rotate {
  0% { transform: rotate(0deg) scale(1); }
  50% { transform: rotate(180deg) scale(1.1); }
  100% { transform: rotate(360deg) scale(1); }
}

@keyframes tech-spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes spin-reverse {
  from { transform: rotate(360deg); }
  to { transform: rotate(0deg); }
}

@keyframes digital-rain {
  0% { 
    opacity: 0; 
    transform: translateY(-20px); 
  }
  50% { 
    opacity: 1; 
    transform: translateY(0px); 
  }
  100% { 
    opacity: 0; 
    transform: translateY(20px); 
  }
}

@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-tech-float {
  animation: tech-float 3s ease-in-out infinite;
}

.animate-tech-rotate {
  animation: tech-rotate 8s ease-in-out infinite;
}

.animate-tech-spin {
  animation: tech-spin 12s linear infinite;
}

.animate-spin-reverse {
  animation: spin-reverse 10s linear infinite;
}

.animate-digital-rain {
  animation: digital-rain 4s ease-in-out infinite;
}

.animate-fade-in-up {
  animation: fade-in-up 0.8s ease-out forwards;
}

.delay-200 { animation-delay: 0.2s; }
.delay-300 { animation-delay: 0.3s; }
.delay-400 { animation-delay: 0.4s; }
.delay-500 { animation-delay: 0.5s; }
.delay-600 { animation-delay: 0.6s; }
.delay-700 { animation-delay: 0.7s; }
.delay-1000 { animation-delay: 1s; }
</style>
