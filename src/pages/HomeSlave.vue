<script setup>
import {computed, inject, onMounted} from 'vue'
import {mapMutations, mapState} from '../stateutil'
import LinkBar from '../components/LinkBar.vue'
import SettingBar from '../components/SettingBar.vue'
import TrendBar from '../components/TrendBar.vue'
const {searchTop, linkList, result, hashLinkList, article } = mapState()


const trendList = computed(() => {
  return searchTop.value.map((target) => {
    return {
      title: target.title,
      link: `/search?q=${encodeURIComponent(target.title)}`
    }
  })
})


</script>

<template>
  <div class="flex flex-col flex-auto md:mr-4 w-[20rem]">
    <div class="slave-top">
      <div class="slave-bar slave-header hidden md:block"></div>
      <SettingBar class="hidden md:flex slave-bar link-tab"></SettingBar>
      <TrendBar class="slave-bar search-tab" :dataList="trendList"></TrendBar>
    </div>
    <LinkBar class="slave-bar link-tab" :linkList="linkList"></LinkBar>
   
  </div>
</template>

<style scoped>


.slave-top >>> .search-tab {
  box-shadow: none;
}

.slave-container >>> .search-relevant .content {
  height: inherit;
}

.slave-container >>> .search-tab .content {
  height: 100%;
  min-height: 16rem;
}


.slave-bar {
  margin-top: 0;
  padding: .5rem;

}

.slave-header {
  height: 19rem;
  background-image: url("../assets/images/motto.gif");
  background-position: center center;
  margin-bottom: 0;
  background-repeat: no-repeat;
}


</style>
