<script setup>
import {mapMutations, mapState} from '../stateutil'
import {computed, inject, onMounted, ref, watch, onUnmounted } from 'vue'
import {useRoute, useRouter} from 'vue-router'
import LoginModal from "../components/LoginModal.vue";
import {useStore} from 'vuex'
import AutoScrollList  from "../components/AutoScrollList.vue";

const { complete, setLan, ready, scrolling } = mapMutations()
const { completeMap, language, setting, popularLinkList, scrolled } = mapState()

const http = inject('http')
const store = useStore()

const tap = ref(false)
const searchForm = ref(null)
const completeBox = ref(null)
const searchContainer = ref(null)
let latestKeyword = ''
let completeIndex = ref(-1);

// 滚动方向检测相关状态
let lastScrollY = 0;

const router = useRouter();
const route = useRoute();



const handleScroll = () => {
  const currentScrollY = window.scrollY;
  
  // window.scrollY 是页面垂直滚动的距离
  // 如果滚动距离大于0，isScrolled.value 就为 true，否则为 false
  scrolling(currentScrollY > 0);
  
  // 检测滚动方向
  if (currentScrollY < lastScrollY) {
    // 向上滚动
    store.commit('updateScrollDirection', 'up');
  } else if (currentScrollY > lastScrollY) {
    // 向下滚动
    store.commit('updateScrollDirection', 'down');
  }
  
  lastScrollY = currentScrollY;
};

// 3. 在组件挂载到 DOM 后，添加滚动事件监听器
onMounted(() => {
  window.addEventListener('scroll', handleScroll);
  // 初始化滚动位置
  lastScrollY = window.scrollY;
});

// 4. 在组件卸载前，移除事件监听器，这是一个好习惯，可以防止内存泄漏
onUnmounted(() => {
  window.removeEventListener('scroll', handleScroll);
});

const keyword = ref('')

watch(() => route.query.q, () => {
  if(route.query.q) {
    keyword.value = route.query.q
  }

})

onMounted(() => {
  fetch(`https://book.lan`, { method: 'GET', mode: 'cors' })
  .then(response => {
    setLan(true)
  })

  fetchNews()
  completeInput()
});


const completeList = computed(() => {
  if(completeMap.value[keyword.value] && completeMap.value[keyword.value].length > 0){
    completeIndex.value = -1
    return completeMap.value[keyword.value]
  }
  return completeMap.value[latestKeyword]
})

let completeInput = async () => {
  const inputWord = keyword.value
  if(completeMap.value[inputWord]){
    if(completeMap.value[inputWord].length > 0){
      latestKeyword = inputWord
    }

    return ;
  }
  let result = await http.get('/news/search/complete?q=' + encodeURIComponent(inputWord))
  complete({
    keyword: inputWord,
    completeList: result.data
  })

  if(completeMap.value[inputWord].length > 0){
    latestKeyword = inputWord
  }
}

let fetchNews = async () => {
  return http.get('/news/data').then(resp => {
    ready(resp.data)
  })
}

let checkSubmit = (e) => {
  if(!keyword.value){
    e.preventDefault()
    return false
  }
  return true
}

let doSubmit = (item) => {
  keyword.value = item
  tap.value = false;
  router.push({ path: '/search', query: { q: keyword.value } })
}

let trySubmit = () => {
  if(!keyword.value){
    return ;
  }
  tap.value = false;
  router.push({ path: '/search', query: { q: keyword.value } })

}

let focusOut = () => {
  if(completeBox.value.matches(":hover")){
    return ;
  }
  if(searchContainer.value.matches(":hover")){
    return ;
  }
  tap.value = false;
}

let shiftUp = () => {
  if(completeIndex.value > 0) {
    completeIndex.value --;
    keyword.value = completeList.value[completeIndex.value];
  }
}

let shiftDown = () => {
  if(completeIndex.value + 1 < completeList.value.length ) {
    completeIndex.value ++;
    keyword.value = completeList.value[completeIndex.value];
  }
}

</script>

<template>
  <div class="sticky-header sticky top-0 z-50 w-full bg-white transition-shadow duration-300 ease-in-out h-12 md:h-15" :class="scrolled ? 'shadow-md': ''">
    <div class="flex space-between items-center my-2 mx-3 gap-4">
  
      <div class="flex flex-auto md:flex-initial gap-4 items-center">
        <router-link class="hidden md:flex ml-2 icon-logo header-link" to="/" ></router-link>
        <router-link class="flex items-center header-link link-tab" to="/" >
          <svg viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="6278" id="mx_n_1749456672934" width="20" height="20"><path d="M928 213.333333h-74.666667V96a53.393333 53.393333 0 0 0-53.333333-53.333333H96a53.393333 53.393333 0 0 0-53.333333 53.333333v704a53.393333 53.393333 0 0 0 53.333333 53.333333h117.333333v74.666667a53.393333 53.393333 0 0 0 53.333334 53.333333h661.333333a53.393333 53.393333 0 0 0 53.333333-53.333333V266.666667a53.393333 53.393333 0 0 0-53.333333-53.333334zM96 810.666667a10.666667 10.666667 0 0 1-10.666667-10.666667V96a10.666667 10.666667 0 0 1 10.666667-10.666667h704a10.666667 10.666667 0 0 1 10.666667 10.666667v704a10.666667 10.666667 0 0 1-10.666667 10.666667z m842.666667 117.333333a10.666667 10.666667 0 0 1-10.666667 10.666667H266.666667a10.666667 10.666667 0 0 1-10.666667-10.666667v-74.666667h544a53.393333 53.393333 0 0 0 53.333333-53.333333V256h74.666667a10.666667 10.666667 0 0 1 10.666667 10.666667z m-256-309.333333a21.333333 21.333333 0 0 1-21.333334 21.333333H234.666667a21.333333 21.333333 0 0 1 0-42.666667h426.666666a21.333333 21.333333 0 0 1 21.333334 21.333334z m0-213.333334a21.333333 21.333333 0 0 1-21.333334 21.333334H533.333333a21.333333 21.333333 0 0 1 0-42.666667h128a21.333333 21.333333 0 0 1 21.333334 21.333333z m0-170.666666a21.333333 21.333333 0 0 1-21.333334 21.333333H533.333333a21.333333 21.333333 0 0 1 0-42.666667h128a21.333333 21.333333 0 0 1 21.333334 21.333334zM213.333333 448V234.666667a21.333333 21.333333 0 0 1 38-13.333334l132.666667 165.853334V234.666667a21.333333 21.333333 0 0 1 42.666667 0v213.333333a21.333333 21.333333 0 0 1-38 13.333333L256 295.48V448a21.333333 21.333333 0 0 1-42.666667 0z" fill="currentColor" p-id="6279"></path></svg>
          <span>要闻</span>
        </router-link>
        <router-link class="flex items-center header-link link-tab" to="/nav" >
          <svg viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="5071" width="20" height="20"><path d="M456 515.2c-4.8 0-8-1.6-11.2-3.2L72 299.2c-11.2-6.4-16-20.8-9.6-32s20.8-16 32-9.6L467.2 472c11.2 6.4 16 20.8 9.6 32-3.2 8-11.2 11.2-20.8 11.2z" p-id="5072" fill="currentColor"></path><path d="M457.6 515.2c-8 0-16-4.8-20.8-12.8-6.4-11.2-1.6-25.6 9.6-32l385.6-216c11.2-6.4 25.6-1.6 32 9.6s1.6 25.6-9.6 32L468.8 512c-3.2 3.2-8 3.2-11.2 3.2z" p-id="5073" fill="currentColor"></path><path d="M841.6 299.2c-3.2 0-8-1.6-11.2-3.2L448 104c-11.2-6.4-16-20.8-11.2-32 6.4-11.2 20.8-16 32-11.2l382.4 192c11.2 6.4 16 20.8 11.2 32-3.2 9.6-11.2 14.4-20.8 14.4z" p-id="5074" fill="currentColor"></path><path d="M89.6 297.6c-8 0-17.6-4.8-20.8-12.8-6.4-11.2-1.6-25.6 11.2-32L448 62.4c11.2-6.4 25.6-1.6 32 11.2s1.6 25.6-11.2 32l-368 188.8c-3.2 1.6-6.4 3.2-11.2 3.2z m369.6 665.6c-4.8 0-8-1.6-12.8-3.2L76.8 734.4c-11.2-6.4-14.4-22.4-8-33.6 6.4-11.2 22.4-14.4 33.6-8L472 918.4c11.2 6.4 14.4 22.4 8 33.6-4.8 8-12.8 11.2-20.8 11.2z" p-id="5075" fill="currentColor"></path><path d="M465.6 963.2c-8 0-16-4.8-20.8-12.8-6.4-11.2-1.6-25.6 9.6-32l129.6-72c11.2-6.4 25.6-1.6 32 9.6 6.4 11.2 1.6 25.6-9.6 32l-129.6 72c-3.2 3.2-8 3.2-11.2 3.2z m377.6-548.8c-12.8 0-24-11.2-24-24V280c0-12.8 11.2-24 24-24s24 11.2 24 24v110.4c0 12.8-11.2 24-24 24z" p-id="5076" fill="currentColor"></path><path d="M465.6 963.2c-12.8 0-24-11.2-24-24V496c0-12.8 11.2-24 24-24s24 11.2 24 24v443.2c0 12.8-11.2 24-24 24zM83.2 732.8c-12.8 0-24-11.2-24-24V291.2c0-12.8 11.2-24 24-24s24 11.2 24 24v417.6c0 12.8-11.2 24-24 24zM374.4 608c-4.8 0-8-1.6-12.8-3.2l-179.2-104c-11.2-6.4-16-20.8-8-33.6 6.4-11.2 20.8-16 33.6-8l179.2 104c11.2 6.4 16 20.8 8 33.6-4.8 8-12.8 11.2-20.8 11.2z m438.4 241.6c-11.2 0-20.8-4.8-27.2-11.2-8-8-22.4-19.2-30.4-19.2s-24 11.2-30.4 19.2c-6.4 8-17.6 11.2-27.2 11.2-4.8 0-9.6-1.6-14.4-3.2h-1.6l-48-27.2H632c-12.8-9.6-19.2-27.2-11.2-43.2 1.6-1.6 3.2-8 3.2-14.4 0-20.8-16-36.8-36.8-36.8h-1.6c-14.4 0-25.6-11.2-28.8-28.8 0 0-4.8-22.4-4.8-40s4.8-40 4.8-40c3.2-16 14.4-28.8 28.8-28.8h1.6c20.8 0 36.8-16 36.8-36.8 0-4.8-3.2-11.2-3.2-14.4-6.4-14.4-1.6-33.6 12.8-43.2l1.6-1.6 49.6-27.2h1.6c4.8-1.6 9.6-3.2 14.4-3.2 9.6 0 20.8 4.8 27.2 11.2 8 8 22.4 17.6 30.4 17.6s22.4-9.6 30.4-17.6c6.4-6.4 17.6-11.2 27.2-11.2 4.8 0 9.6 1.6 14.4 3.2h1.6l48 27.2h1.6c12.8 9.6 19.2 27.2 11.2 43.2-1.6 1.6-3.2 8-3.2 14.4 0 20.8 16 36.8 36.8 36.8h1.6c14.4 0 25.6 11.2 28.8 28.8 0 0 4.8 22.4 4.8 40s-4.8 40-4.8 40c-3.2 17.6-14.4 28.8-28.8 28.8h-1.6c-20.8 0-36.8 16-36.8 36.8 0 4.8 3.2 11.2 3.2 14.4 6.4 14.4 1.6 33.6-12.8 43.2l-1.6 1.6-49.6 27.2h-1.6c-4.8 1.6-9.6 3.2-14.4 3.2z m-59.2-80c16 0 32 6.4 48 17.6 4.8 3.2 9.6 8 12.8 9.6l28.8-16c-1.6-6.4-3.2-14.4-3.2-22.4 0-41.6 30.4-76.8 70.4-83.2 1.6-6.4 1.6-14.4 1.6-20.8s-1.6-14.4-1.6-20.8c-40-6.4-70.4-41.6-70.4-84.8 0-8 1.6-16 3.2-22.4l-28.8-16c-3.2 3.2-8 6.4-12.8 9.6-16 11.2-32 17.6-46.4 17.6-14.4 0-30.4-6.4-46.4-17.6-4.8-3.2-9.6-6.4-12.8-9.6l-30.4 16c1.6 6.4 3.2 14.4 3.2 22.4 0 41.6-30.4 76.8-70.4 84.8-1.6 6.4-1.6 16-1.6 20.8 0 6.4 1.6 14.4 1.6 20.8 40 6.4 70.4 41.6 70.4 83.2 0 8-1.6 16-3.2 22.4l27.2 14.4c3.2-3.2 8-6.4 12.8-9.6 17.6-9.6 33.6-16 48-16z m0-35.2c-43.2 0-80-35.2-80-80 0-43.2 35.2-80 80-80 43.2 0 80 35.2 80 80s-36.8 80-80 80z m0-110.4c-17.6 0-30.4 14.4-30.4 30.4s14.4 30.4 30.4 30.4S784 672 784 656s-12.8-32-30.4-32z" p-id="5077" fill="currentColor"></path></svg>
          <span>资源</span>
        </router-link>
        <router-link class="flex items-center header-link link-tab" to="/news" >
          <svg viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="13541" width="20" height="20"><path d="M335.4 128 224 128l-67.7 0c-15.5 0-28.2 13-28.2 28.5l0 0.8L128.1 299l0 0.8 0 0.8 0 141.7 0 0.6c0 15.6 12.7 28.3 28.2 28.3L224 471.2l111.4 0c72 0 91.5-123.7 54.2-171.4C426.1 252.1 407.8 128 335.4 128zM224 192l109 0c3.1 3.8 8.5 14.5 10.3 31.2 2.1 19-1.9 33.9-4.5 37.7l-4.7 6.1L192 267 192 192 224 192zM343.5 376.2c-1.9 16.6-7.4 27.1-10.5 30.9L224 407.1l-32 0 0-76.1 140.8 0 6.3 8.1C341.7 342.8 345.7 357.5 343.5 376.2z" p-id="13542" fill="currentColor"></path><path d="M196 640l-69.6 0c-34.2 0-62.1-27.8-62.1-62L64.3 119.8c3.1-28.7 26.8-52.4 55.7-55.5l457.8 0c34.2 0 62.1 27.7 62.1 61.8L639.9 287c0 17.7 14.3 32 32 32s32-14.3 32-32L703.9 126.1C704 56.8 647.4 0.3 577.9 0.3L158.4 0.3 158.4 0l-32 0c-33.4 0-65 13.2-89 37.1C13.3 61.1 0 92.7 0 126.1l0 32 0.3 0L0.3 578c0 69.5 56.6 126 126.1 126L196 704c17.7 0 32-14.3 32-32S213.7 640 196 640z" p-id="13543" fill="currentColor"></path><path d="M993.6 556.8c-19.1-34.4-46.2-65.1-80.6-91.3C844 413 752.7 384 656 384s-188 29-257 81.5c-34.4 26.2-61.5 56.9-80.6 91.3C298.2 593.2 288 631.9 288 672c0 41.1 10.8 80.8 32 117.9 15.8 27.7 36.8 52.9 62.7 75.3l-58.6 112c-6.5 12.3-4.2 27.4 5.6 37.3 6.2 6.2 14.4 9.5 22.8 9.5 4.9 0 9.9-1.1 14.5-3.5l156.7-79.6C565.8 953.6 610.2 960 656 960c96.7 0 188-29 257-81.5 34.4-26.2 61.5-56.9 80.6-91.3 20.2-36.4 30.4-75.1 30.4-115.2S1013.8 593.2 993.6 556.8zM874.2 827.6C816.3 871.7 738.8 896 656 896c-43.5 0-85.6-6.7-125-19.9-8.1-2.7-17-2.1-24.7 1.8l-79.1 40.2 24.4-46.6c7.3-14 3.3-31.3-9.5-40.7C384 788.3 352 731.9 352 672c0-58.2 30.5-113.4 85.8-155.6C495.7 472.3 573.2 448 656 448s160.3 24.3 218.2 68.4C929.5 558.6 960 613.8 960 672S929.5 785.4 874.2 827.6z" p-id="13544" fill="currentColor"></path><path d="M803 576 483 576c-17.7 0-32 14.3-32 32s14.3 32 32 32l320 0c17.7 0 32-14.3 32-32S820.7 576 803 576z" p-id="13545" fill="currentColor"></path><path d="M803 704 483 704c-17.7 0-32 14.3-32 32s14.3 32 32 32l320 0c17.7 0 32-14.3 32-32S820.7 704 803 704z" p-id="13546" fill="currentColor"></path></svg>
          <span>时评</span>
        </router-link>
        <router-link class="hidden md:flex items-center header-link link-tab" to="/a" >
          <svg viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1556" width="20" height="20"><path d="M928 161H699.2c-49.1 0-97.1 14.1-138.4 40.7L512 233l-48.8-31.3C422 175.1 373.9 161 324.8 161H96c-17.7 0-32 14.3-32 32v568c0 17.7 14.3 32 32 32h228.8c49.1 0 97.1 14.1 138.4 40.7l44.4 28.6c1.3 0.8 2.8 1.3 4.3 1.3s3-0.4 4.3-1.3l44.4-28.6C602 807.1 650.1 793 699.2 793H928c17.7 0 32-14.3 32-32V193c0-17.7-14.3-32-32-32zM324.8 721H136V233h188.8c35.4 0 69.8 10.1 99.5 29.2l48.8 31.3 6.9 4.5v462c-47.6-25.6-100.8-39-155.2-39z m563.2 0H699.2c-54.4 0-107.6 13.4-155.2 39V298l6.9-4.5 48.8-31.3c29.7-19.1 64.1-29.2 99.5-29.2H888v488z" p-id="1557" fill="currentColor"></path><path d="M396.9 361H211.1c-3.9 0-7.1 3.4-7.1 7.5v45c0 4.1 3.2 7.5 7.1 7.5h185.7c3.9 0 7.1-3.4 7.1-7.5v-45c0.1-4.1-3.1-7.5-7-7.5z m223.1 7.5v45c0 4.1 3.2 7.5 7.1 7.5h185.7c3.9 0 7.1-3.4 7.1-7.5v-45c0-4.1-3.2-7.5-7.1-7.5H627.1c-3.9 0-7.1 3.4-7.1 7.5zM396.9 501H211.1c-3.9 0-7.1 3.4-7.1 7.5v45c0 4.1 3.2 7.5 7.1 7.5h185.7c3.9 0 7.1-3.4 7.1-7.5v-45c0.1-4.1-3.1-7.5-7-7.5z m416 0H627.1c-3.9 0-7.1 3.4-7.1 7.5v45c0 4.1 3.2 7.5 7.1 7.5h185.7c3.9 0 7.1-3.4 7.1-7.5v-45c0.1-4.1-3.1-7.5-7-7.5z" p-id="1558" fill="currentColor"></path></svg>
          <span>阅读</span>
        </router-link>
        <router-link class="flex items-center header-link link-tab" to="/note" >
          <svg t="1750145473104" fill="currentColor" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="13218" width="20" height="20"><path d="M561.41 73.24h0.06c61.69 0 113.61 13.43 158.72 41.05 41.18 25.22 75.92 61.76 106.2 111.7 28 46.15 51.21 102.22 71 171.4C914 455.34 928.19 523 940.77 604.3c16.12 104.19 23 183.77 26 233.66h-2.19c-11.49 0-46.23 0-68.61 25.32-5.8 5.65-33.82 27.12-73.23 46.86-32.93 16.5-82.51 36.16-131.73 36.16-7.05 0-8.6-1.65-9.35-2.44-7.12-7.52-10.09-29.23-9.46-41.55l5-56.89H518.25l-70.77-1.08 19.21 67.58c2.56 11.08 1.88 26.46-1.44 30.64-0.76 1-5.88 3.73-19 3.73-31.09 0-78.6-15.61-133.76-43.95a828.67 828.67 0 0 1-80.76-47.52l-13.65-9.41H56.25c5.87-76.65 22.8-223.63 70.79-369.47 39.49-120 91.81-214.9 155.52-282 36.34-38.3 76.89-67.82 120.51-87.75 47.82-21.85 101.09-32.92 158.32-32.92m0.08-53.27C16.8 19.94 0 898.65 0 898.65h201.55s146.36 100.87 244.7 100.87 72-100.87 72-100.87h100.78s-7.22 100.87 71.93 100.87c115.19 0 230.3-84 244.74-100.87 4.81-5.6 16-7.47 28.83-7.47 25.61 0 57.61 7.47 57.61 7.47s1.08-109.56-28.75-302.49C948.09 303.65 858.72 20 561.47 20z" p-id="13219" ></path><path d="M620.44 158c34.66 0 66.25 6.78 93.9 20.16 25.85 12.51 48.45 30.86 67.16 54.55 38.17 48.31 59.18 118 59.18 196.19 0 79.25-2.38 154.29-31.14 201.45-18.17 29.79-57.54 69.39-189.1 69.39-52 0-94.42-7.05-126-21-25.71-11.31-45-27.44-58.9-49.32-31-48.75-35.32-125.2-35.32-200.57a321 321 0 0 1 18.4-108.26c11.63-32.59 28.13-61.72 49.13-86.59 41.37-49 95.6-76 152.69-76m0-53.24c-151 0-273.49 145.14-273.49 324.14s25.24 324.08 273.49 324.08c260.83 0 273.48-145.08 273.48-324.08S796.7 104.71 620.44 104.71z" p-id="13220" ></path><path d="M447.05 353.6a72 72 0 1 0 72-72 72 72 0 0 0-72 72z m172.81 280.89c0 19.91 19.33 36 43.18 36s43.21-16.09 43.21-36-19.33-36-43.19-36-43.18 16.14-43.18 36z m86.39-280.89c0 39.76 29 72 64.78 72s64.78-32.24 64.78-72-29-72-64.78-72-64.78 32.24-64.78 72z" p-id="13221" ></path></svg>
          <span>树洞</span>
        </router-link>
      </div>

      <div class="hidden xl:flex flex-2 overflow-hidden">
        <AutoScrollList :items="popularLinkList"/>
      </div>

      <div ref="searchContainer" class="hidden md:flex flex-1 items-center " >
      
        <form class="flex flex-auto relative" ref="searchForm" action="/search" method="get"  autocomplete="off" :class="tap ? 'search-bar-focus' : ''" @keyup.down="shiftDown" @keyup.up="shiftUp" @keyup.enter="trySubmit" @focusin="tap=true; completeInput()"  @focusout="focusOut()" tabindex="0">
          <div class="flex flex-auto">
            <input name="q" class="search-bar flex-auto" placeholder="搜索关键字" v-on:input="completeInput()" v-model="keyword" />
            <button class="submit-btn" type="button" v-on:click="checkSubmit()">
              <svg class="submit-icon" focusable="false" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                <path d="M15.5 14h-.79l-.28-.27A6.471 6.471 0 0 0 16 9.5 6.5 6.5 0 1 0 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
              </svg>
            </button>
            <div ref="completeBox" class="auto-complete-box" >
              <div class="auto-complete-item" v-for="(item, index) in completeList" v-on:click="doSubmit(item)" :class="(index === completeIndex) ? 'curr' : ''">
                <span>{{item}}</span>
              </div>
            </div>
          </div>
        </form>
      </div>
      
      <!-- 用户模块现在在LoginModal组件中处理 -->
      <LoginModal />
    </div>
    
  </div>

</template>

<style scoped>

.header-link {
  align-items: center;
  gap: 4px;
  color: #333;
  font-weight: bold;
  position: relative;
}

.header-link:hover {
   color: #37f;
  text-decoration: none;
}

.header-link span {
  word-break: keep-all;
}


.link-tab.router-link-active {
  color: #37f;
}

.link-tab.router-link-active:before {
  background: #37f;
  border-radius: 2px;
  bottom: -6px;
  content: "";
  height: 3px;
  left: 70%;
  position: absolute;
  -webkit-transform: translateX(-50%);
  -ms-transform: translateX(-50%);
  transform: translateX(-50%);
  width: 24px;
}


.icon-logo {
  width: 120px;
  height: 40px;
  background: url(data:image/png;base64,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);
}

.search-bar {
  display: block;
  position: relative;
  padding: 12px 50px 12px 12px;
  background-color: rgba(242, 248, 255, 0.5);
  border: none;
  font-size: 14px;
  color: #777;
  transition: .1s;
  outline: none;
  flex: 1;
}

.search-bar-focus .search-bar{
  background-color: #fff;
  box-shadow: 0 0 20px rgba(0, 77, 153, 0.2);
}

.submit-btn {
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1;
  margin-left: -50px;
  width: 50px;
  background-color: transparent;
  height: 40px;
  border-radius: 0;
}

.search-bar-focus .submit-btn .submit-icon{
  color: #06c;
}

.submit-icon {
  color: #ccc;
  fill: currentColor;
  height: 32px;
  width: 32px;
}

.search-bar-focus .auto-complete-box {
  display: block;
}

.auto-complete-box {
  display: none;
  position: absolute;
  left: 0;
  right: 0;
  top: calc(100% + 1px);
  z-index: 999;
  box-shadow: 0 10px 10px rgba(0, 77, 153, 0.2);
  background-color: #fff;
  color: #666;
}

.auto-complete-item {
  cursor: pointer;
  padding: .5rem 1rem;
}

.auto-complete-item.curr, .auto-complete-item:hover{
  background-color: #f3f9ff;
}
</style>
