<script setup>
import { inject, onMounted, ref, computed} from 'vue'
import { mapState, mapGetters, mapMutations, mapActions } from '../stateutil'
import HotBar from "../components/HotBar.vue"
import { VueDraggableNext as draggable } from 'vue-draggable-next'
import { useStore } from 'vuex'
import SkeletonItem from "../components/SkeletonItem.vue";

const store = useStore();
const { ready, sorted } = mapMutations()

const { sogaTop, editable, setting } = mapState()

const dataList = computed({
  get() {
    return store.state.sogaTop;
  },
  set(value) {
    store.commit("sorted", value);
  }
})

const subscribe = (sourceKey) => {
  store.commit("subscribe", sourceKey);
}

const dropable = computed(() => {
  return store.state.sogaTop.reduce(((previousValue, target) => {
    if(!target.hide) {
      return previousValue + 1
    }
    return previousValue
  }), 0) > 8
});

</script>

<template>
  <draggable class="flex-auto grid grid-cols-1 gap-0 lg:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-4 mx-0 md:mx-4 md:mb-0 mb-4" v-model="dataList" item-key="sourceKey" :disabled="!editable" tag="transition-group"  >
     <template v-if="!dataList.length">
       <SkeletonItem :rows="8" v-for="i in 12"></SkeletonItem>
     </template>
     <template v-for="element in dataList">
        <HotBar v-show="editable || !element.hide" @subscribe="subscribe(element.sourceKey)" :title="element.name" :update="element.lastModifyTime" :linkList="element.data" :sourceKey="element.sourceKey" :iconColor="element.iconColor" :hide="element.hide" :editable="editable" :dropable="dropable" :overhidden="setting.hideTop10"></HotBar>
     </template>
  </draggable>

</template>


