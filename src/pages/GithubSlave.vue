<script setup>

import { defineProps } from 'vue';
import LinkItemList from "../components/LinkItemList.vue";
import DocumentStructure from "../components/DocumentStructure.vue"; 

defineProps({
  articleList: Array,
  // Add headings and activeHeadingId props
  headings: {
    type: Array,
    default: () => []
  },
  activeHeadingId: {
    type: String,
    default: null
  }
})

</script>

<template>
    <div class="flex flex-col flex-auto overflow-hidden">
      <!-- Re-add DocumentStructure rendering, pass props -->
      <DocumentStructure
        v-if="headings && headings.length > 0"
        :headings="headings"
        :active-heading-id="activeHeadingId"
        title="文档目录"
      ></DocumentStructure> <!-- Explicit closing tag and remove comment attribute -->

      <!-- 文件列表 -->
      <LinkItemList
        v-if="articleList && articleList.length > 0"
        :linkList="articleList"
      />
    </div>
</template>

<style scoped>


</style>
