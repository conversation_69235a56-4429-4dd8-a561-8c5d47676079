<template>
  <div class="min-h-screen bg-gradient-to-br from-white via-blue-50 to-gray-50 flex items-center justify-center p-4 overflow-hidden relative">
    <!-- 科技背景网格 -->
    <div class="absolute inset-0 opacity-5">
      <div class="absolute inset-0" style="background-image: linear-gradient(rgba(59, 130, 246, 0.3) 1px, transparent 1px), linear-gradient(90deg, rgba(59, 130, 246, 0.3) 1px, transparent 1px); background-size: 50px 50px;"></div>
    </div>

    <!-- 动态背景元素 -->
    <div class="absolute inset-0 overflow-hidden">
      <div class="absolute -top-40 -right-40 w-80 h-80 bg-blue-100/60 rounded-full filter blur-3xl animate-pulse"></div>
      <div class="absolute -bottom-40 -left-40 w-80 h-80 bg-blue-200/40 rounded-full filter blur-3xl animate-pulse delay-1000"></div>
      <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-60 h-60 bg-blue-50/80 rounded-full filter blur-2xl animate-bounce"></div>
    </div>

    <!-- 科技风格线条 -->
    <div class="absolute inset-0 pointer-events-none">
      <div class="absolute top-20 left-0 w-full h-px bg-gradient-to-r from-transparent via-blue-300/40 to-transparent animate-pulse"></div>
      <div class="absolute bottom-20 left-0 w-full h-px bg-gradient-to-r from-transparent via-blue-300/40 to-transparent animate-pulse delay-500"></div>
      <div class="absolute left-20 top-0 w-px h-full bg-gradient-to-b from-transparent via-blue-200/30 to-transparent animate-pulse delay-300"></div>
      <div class="absolute right-20 top-0 w-px h-full bg-gradient-to-b from-transparent via-blue-200/30 to-transparent animate-pulse delay-700"></div>
    </div>

    <!-- 浮动科技元素 -->
    <div class="absolute inset-0 pointer-events-none">
      <div class="absolute top-20 left-20 w-3 h-3 bg-blue-400 rounded-full animate-tech-float delay-300 shadow-lg shadow-blue-400/30"></div>
      <div class="absolute top-40 right-32 w-4 h-4 bg-blue-500 rounded-full animate-tech-float delay-700 shadow-lg shadow-blue-500/30"></div>
      <div class="absolute bottom-32 left-40 w-2 h-2 bg-blue-600 rounded-full animate-tech-float delay-1000 shadow-lg shadow-blue-600/30"></div>
      <div class="absolute bottom-20 right-20 w-3 h-3 bg-blue-400 rounded-full animate-tech-float delay-500 shadow-lg shadow-blue-400/30"></div>
      
      <!-- 科技风格方块 -->
      <div class="absolute top-1/4 left-10 w-6 h-6 border border-blue-300/60 animate-tech-rotate"></div>
      <div class="absolute top-3/4 right-10 w-4 h-4 border border-blue-400/60 animate-tech-rotate delay-500"></div>
      <div class="absolute top-1/2 left-1/4 w-5 h-5 border border-blue-500/50 animate-tech-rotate delay-1000"></div>
    </div>

    <!-- 主要内容 -->
    <div class="relative z-10 text-center max-w-2xl mx-auto">
      <!-- 纸飞机动画区域 -->
      <div class="mb-12 relative">
        <div class="paper-plane-container mx-auto">
          <div class="paper-plane">
            <div class="plane-left"></div>
            <div class="plane-right"></div>
            <div class="plane-bottom"></div>
          </div>
          <div class="plane-path"></div>
        </div>
      </div>

      <!-- 错误代码 -->
      <div class="mb-6">
        <h1 class="text-8xl md:text-9xl font-black bg-gradient-to-r from-blue-600 via-blue-700 to-blue-800 bg-clip-text text-transparent leading-none animate-pulse font-mono tracking-wider">
          404
        </h1>
        <div class="flex justify-center mt-2">
          <div class="h-1 w-24 bg-gradient-to-r from-transparent via-blue-500 to-transparent animate-pulse"></div>
        </div>
      </div>

      <!-- 主要错误信息 -->
      <div class="mb-8">
        <h2 class="text-2xl md:text-3xl font-bold text-blue-800 mb-4 animate-fade-in-up">
          页面不存在
        </h2>
        <p class="text-lg text-blue-600 leading-relaxed animate-fade-in-up delay-200 font-light">
          抱歉，您访问的页面不存在或已被移除
        </p>
      </div>

      <!-- 操作按钮 -->
      <div class="flex justify-center animate-fade-in-up delay-500">
        <button 
          @click="goHome" 
          class="group relative px-8 py-4 bg-gradient-to-r from-blue-500 to-blue-600 text-white font-semibold rounded-2xl shadow-lg shadow-blue-500/30 hover:shadow-xl hover:shadow-blue-500/40 transform hover:-translate-y-1 transition-all duration-300 ease-out focus:outline-none focus:ring-4 focus:ring-blue-400/50 border border-blue-400/30"
        >
          <span class="relative z-10 flex items-center space-x-2">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
            </svg>
            <span>返回首页</span>
          </span>
          <div class="absolute inset-0 bg-gradient-to-r from-blue-600 to-blue-700 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
        </button>
      </div>

      <!-- 底部状态信息 -->
      <div class="mt-12 text-center animate-fade-in-up delay-700">
        <div class="flex justify-center space-x-8 text-xs text-blue-500">
          <span class="flex items-center space-x-2">
            <div class="w-2 h-2 bg-blue-400 rounded-full animate-pulse shadow-sm shadow-blue-400/50"></div>
            <span class="font-mono">路径检测</span>
          </span>
          <span class="flex items-center space-x-2">
            <div class="w-2 h-2 bg-blue-500 rounded-full animate-pulse delay-300 shadow-sm shadow-blue-500/50"></div>
            <span class="font-mono">智能导航</span>
          </span>
          <span class="flex items-center space-x-2">
            <div class="w-2 h-2 bg-blue-600 rounded-full animate-pulse delay-600 shadow-sm shadow-blue-600/50"></div>
            <span class="font-mono">安全返回</span>
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router';

const router = useRouter();

const goHome = () => {
  router.push('/');
};
</script>

<style scoped>
/* 纸飞机动画样式 */
.paper-plane-container {
  position: relative;
  width: 200px;
  height: 120px;
}

.paper-plane {
  position: absolute;
  top: 50%;
  left: 0;
  width: 60px;
  height: 30px;
  transform: translateY(-50%);
  animation: fly 4s infinite ease-in-out;
}

.plane-left {
  position: absolute;
  top: 0;
  left: 0;
  border-top: 30px solid #3b82f6;
  border-left: 30px solid transparent;
  border-right: 30px solid transparent;
  transform: rotate(135deg);
}

.plane-right {
  position: absolute;
  top: 0;
  left: 30px;
  border-top: 30px solid #3b82f6;
  border-left: 30px solid transparent;
  border-right: 30px solid transparent;
  transform: rotate(45deg);
}

.plane-bottom {
  position: absolute;
  top: 15px;
  left: 15px;
  border-top: 15px solid #60a5fa;
  border-left: 15px solid transparent;
  border-right: 15px solid transparent;
  transform: rotate(-45deg);
}

.plane-path {
  position: absolute;
  top: 50%;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(90deg, rgba(59, 130, 246, 0.2) 0%, rgba(59, 130, 246, 0) 100%);
  transform: translateY(-50%);
  z-index: -1;
}

@keyframes fly {
  0% {
    left: 0;
    transform: translateY(-50%) rotate(0deg);
  }
  25% {
    transform: translateY(-60%) rotate(5deg);
  }
  50% {
    transform: translateY(-50%) rotate(0deg);
  }
  75% {
    transform: translateY(-40%) rotate(-5deg);
  }
  100% {
    left: 140px;
    transform: translateY(-50%) rotate(0deg);
  }
}

/* 科技动画效果 */
@keyframes tech-float {
  0%, 100% { 
    transform: translateY(0px) scale(1); 
    opacity: 0.8;
  }
  50% { 
    transform: translateY(-20px) scale(1.1); 
    opacity: 1;
  }
}

@keyframes tech-rotate {
  0% { transform: rotate(0deg) scale(1); }
  50% { transform: rotate(180deg) scale(1.1); }
  100% { transform: rotate(360deg) scale(1); }
}

@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-tech-float {
  animation: tech-float 3s ease-in-out infinite;
}

.animate-tech-rotate {
  animation: tech-rotate 8s ease-in-out infinite;
}

.animate-fade-in-up {
  animation: fade-in-up 0.8s ease-out forwards;
}

.delay-200 { animation-delay: 0.2s; }
.delay-300 { animation-delay: 0.3s; }
.delay-500 { animation-delay: 0.5s; }
.delay-700 { animation-delay: 0.7s; }
.delay-1000 { animation-delay: 1s; }
</style>