<script setup>
// Re-add onUnmounted, defineEmits; Remove DocumentStructure import
import {inject, nextTick, onMounted, onUnmounted, ref, computed, defineProps, watch, defineEmits} from 'vue'
import { mapState } from '../stateutil.js'; // Keep only mapState
import { useRoute, useRouter } from 'vue-router'
import MarkdownIt from 'markdown-it'
import slugify from 'slugify'
import hljs from 'highlight.js'
import 'highlight.js/styles/atom-one-light.min.css' // Add highlight.js dark theme
import SkeletonItem from "../components/SkeletonItem.vue";
import AppLink from "../components/AppLink.vue";
import Toast from "../components/Toast.vue";
import pinyin from 'pinyin';

const router = useRouter();
const route = useRoute();

const http = inject('http')

const { popularLinkList } = mapState()

const toastRef = ref(null)
const articleContentRef = ref(null)
const headings = ref([])
const activeHeadingId = ref(null) // Re-add activeHeadingId for comparison in handleScroll

// Add emit definitions for headings and active ID
const emit = defineEmits(['update:headings', 'update:activeHeadingId'])

const props = defineProps({
  article: Object,
  fetching: Boolean
})


// 创建 markdown-it 实例并配置
const md = new MarkdownIt({
  html: true,        // 启用 HTML 标签
  breaks: true,      // 将 \n 转换为 <br>
  linkify: true,
  highlight: (str, lang) => {
    if (lang && hljs.getLanguage(lang)) {
      try {
        const code = hljs.highlight(str, { language: lang, ignoreIllegals: true }).value;
        return code;
      } catch (_) {}
    }
    return md.utils.escapeHtml(str);
  }
});

// 使用 slugify 生成 ID
const slugifyTitle = (title) => {
  const pinyinedTitle = pinyin(title, {
    style: pinyin.STYLE_NORMAL, // 普通风格，不带声调
  }).flat().join('-');

  return slugify(pinyinedTitle, { lower: true, strict: true });
};

// 自定义标题渲染，添加 ID
md.renderer.rules.heading_open = (tokens, idx, options, env, self) => {
   // 1. 在 env 对象上初始化一个用于存储已使用 slug 的地方
  //    我们用一个对象（字典）来存储，键是 slug，值是它出现的次数
  if (env.used_slugs === undefined) {
    env.used_slugs = {};
  }

  const token = tokens[idx];
  const title = tokens[idx + 1].content;
  
  // 2. 生成基础 slug
  //    我们还处理了 slug 可能为空的情况，给一个备用名
  let slug = slugifyTitle(title) || 'heading';

  // 3. 检查 slug 是否已经存在，如果存在，则添加后缀
  if (env.used_slugs[slug] !== undefined) {
    // 如果 "introduction" 已经存在，计数器会增加
    env.used_slugs[slug]++;
    // 新的 slug 会变成 "introduction-2", "introduction-3" ...
    // 注意：这里我们用出现的次数作为后缀，比简单地 -1, -2 更可靠
    slug = `${slug}-${env.used_slugs[slug]}`;
  } else {
    // 如果是第一次出现，记录下来，次数为 1
    env.used_slugs[slug] = 1;
  }
  token.attrSet('id', slug);
  return self.renderToken(tokens, idx, options);
};


md.renderer.rules.fence = function (tokens, idx) {
  const token = tokens[idx];
  const content = md.options.highlight(token.content, token.info.trim() || '');
  return `
<div class="code-block-container">
  <button class="copy-btn" title="复制代码">
    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
      <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
      <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
    </svg>
  </button>
  <pre class="hljs code-block-wrapper"><code class="language-${token.info.trim()}">${content}</code></pre>
</div>
`;
};

// 安全解码Base64
const safeDecodeBase64 = (base64String) => {
  try {
    // 使用 TextDecoder 解码，处理 UTF-8 编码
    const binaryString = atob(base64String);
    const bytes = new Uint8Array(binaryString.length);
    for (let i = 0; i < binaryString.length; i++) {
      bytes[i] = binaryString.charCodeAt(i);
    }
    return new TextDecoder('utf-8').decode(bytes);
  } catch (e) {
    console.error('Base64解码失败:', e);
    return '内容解码失败';
  }
};

// 计算文件夹列表
const directories = computed(() => {
  // Use props.article
  if (props.article && props.article.type === 'dir' && props.article.entries) {
    return props.article.entries.filter(entry => entry.type === 'dir');
  }
  return [];
});

// 计算文件列表
const files = computed(() => {
  // Use props.article
  if (props.article && props.article.type === 'dir' && props.article.entries) {
    return props.article.entries.filter(entry => entry.type === 'file');
  }
  return [];
});

//上级目录
const parentDir = computed(() => {
  // Use props.article
  if (props.article && props.article.type === 'dir') {
    return props.article.path.split('/').slice(0, -1).join('/');
  }
  return '';
});

// 渲染文件内容
const renderedContent = computed(() => {
  // Use props.article
  if (props.article && props.article.type === 'file' && props.article.content) {
    // 使用安全的Base64解码方法
    const content = safeDecodeBase64(props.article.content);
    // 渲染Markdown
    return md.render(content);
  }
  return '无法显示内容';
});

// 提取标题信息
const extractHeadings = () => {
  nextTick(() => {
    if (articleContentRef.value) {
      const newHeadings = [];
      const headingElements = articleContentRef.value.querySelectorAll('h1, h2, h3, h4, h5, h6');

      headingElements.forEach(el => {
       
        let uniqueId = el.id;
        // Push the heading info with the *unique* ID
        newHeadings.push({
          id: uniqueId, // Use the unique ID
          title: el.innerText,
          level: parseInt(el.tagName.substring(1)),
          el: el,
          url: `#${uniqueId}` // Use the unique ID for the URL too
        }); // Correct closing parenthesis for push
      }); // Correct closing parenthesis for forEach

      headings.value = newHeadings;
      // 发送事件给父组件
      emit('update:headings', headings.value);
    } else {
       // 如果内容区域不存在，确保发送空数组
       if (headings.value.length > 0) {
         headings.value = [];
         emit('update:headings', []);
       }
    } // Correct closing brace for 'if (articleContentRef.value)'
  }); // Correct closing parenthesis and brace for nextTick
};

// Re-add handleScroll function, but emit the active ID
const handleScroll = () => {
    const header = document.querySelector('.sticky-header');
    // 如果找不到 header 或者 headings 列表为空，则不执行
    if (!header || !headings.value || headings.value.length === 0) {
        // 如果没有 headings，确保 activeId 为 null
        if (activeHeadingId.value !== null) {
            activeHeadingId.value = null;
            emit('update:activeHeadingId', null);
        }
        return;
    }

    const headerHeight = header.offsetHeight;
    // 定义激活区域的顶部边界，略低于 Header
    const activationThreshold = headerHeight + 20; // 10px 额外偏移

    let currentActiveId = null;

    // 遍历标题，找到第一个顶部在激活区域内的标题
    for (let i = 0; i < headings.value.length; i++) {
        const heading = headings.value[i];
        if (heading.el) {
            const rect = heading.el.getBoundingClientRect();
            // 如果标题的顶部在视口内并且低于激活阈值
            if (rect.top < activationThreshold) {
                // 这个标题的顶部在阈值线或阈值线之上
                currentActiveId = heading.id;
                // 继续检查，后面的标题如果也满足条件，会覆盖这个ID，
                // 这样就能确保找到最下方一个满足条件的标题
            } else {
                 // 一旦遇到一个标题顶部低于阈值线，说明之前的 currentActiveId 就是正确的
                 // （它是最后一个顶部在阈值线之上的标题）
                 break;
            }
        }
    }
     // 循环结束后，currentActiveId 要么是最后一个顶部在阈值线之上的标题的 ID，要么是 null

    // 如果页面滚动到了最顶部（或接近顶部），并且第一个标题在阈值线以下，
    // 那么不应该有任何标题高亮
     if (window.scrollY < 50 && headings.value[0]?.el && headings.value[0].el.getBoundingClientRect().top > activationThreshold) {
        currentActiveId = null;
     }


    // Emit the new active ID if it changed
    if (activeHeadingId.value !== currentActiveId) {
       activeHeadingId.value = currentActiveId; // Update local ref for comparison
       emit('update:activeHeadingId', currentActiveId);
    }
};

// 设置复制代码按钮功能
const setupCodeCopy = () => {
  setTimeout(() => {
    const container = articleContentRef.value;
    if (!container) return;
    const copyButtons = container.querySelectorAll('.copy-btn');
    copyButtons.forEach(button => {
      // 使用克隆节点的方式来确保移除旧监听器
      const newButton = button.cloneNode(true);
      button.parentNode.replaceChild(newButton, button);

      newButton.addEventListener('click', () => {
        const codeBlock = newButton.nextElementSibling;
        const code = codeBlock?.textContent || '';
        navigator.clipboard.writeText(code).then(() => {
           if (toastRef.value) {
             const rect = newButton.getBoundingClientRect();
             // 根据 Toast.vue 的 transform 计算，传递按钮顶部中心点的坐标
             const toastX = rect.left + rect.width / 2;
             const toastY = rect.top;
             toastRef.value.show('复制成功!', toastX, toastY);
           }
        }).catch(err => console.error('Failed to copy: ', err));
      });
    });
  }, 100); // 稍增加延迟
};

// 监听 article 变化
watch(() => props.article, (newArticle) => {
  if (newArticle && newArticle.type === 'file') {
    nextTick(() => {
      extractHeadings();
      setupCodeCopy();
      // handleScroll(); // Don't call handleScroll directly here, rely on listener
    });
  } else {
    headings.value = []; // 清空标题
    activeHeadingId.value = null;
  }
}, { deep: true, immediate: true }); // 初始加载时也执行

// Re-add scroll listener on mount
onMounted(() => {
   // Initial check
   if (props.article && props.article.type === 'file') {
     nextTick(() => {
       extractHeadings();
       setupCodeCopy();
       // Initial scroll check after content is ready
       handleScroll();
     });
   }
   // Add listener
   window.addEventListener('scroll', handleScroll);
});

// Re-add removing scroll listener on unmount
onUnmounted(() => {
  window.removeEventListener('scroll', handleScroll);
});


</script>

<template>
  <!-- Keep the simplified template without the sidebar column -->
  <div class="flex flex-auto flex-col">
    <!-- 主内容区域 -->
    <div class="flex flex-auto flex-col justify-between">
        <template v-if="!fetching">
          <!-- 目录类型内容 (Ensure markdown-body class exists) -->
          <div v-if="props.article && props.article.type === 'dir'" class="article-body markdown-body p-4"> <!-- Added padding for visibility -->
            
            <div class="directory-listing">
              
              <!-- 文件夹部分 -->
              <div class="directory-section">
                <div class="directory-item" v-if="props.article?.path">
                  <AppLink :to="`/${route.params.repo}/${ parentDir}`" class="directory-link">
                    <span class="directory-icon">📁</span>
                    <span class="directory-name">上一级</span>
                  </AppLink>
                </div>
                <div v-if="directories.length > 0" v-for="dir in directories" :key="dir.path" class="directory-item">
                  <AppLink :to="`/${route.params.repo}/${dir.path}`" class="directory-link">
                    <span class="directory-icon">📁</span>
                    <span class="directory-name">{{ dir.name }}</span>
                  </AppLink>
                </div>
              </div>

              <!-- 文件部分 -->
              <div v-if="files.length > 0" class="file-section">
                <div v-for="file in files" :key="file.path" class="file-item">
                  <AppLink :to="`/${route.params.repo}/${file.path}`" class="file-link">
                    <span class="file-icon">📄</span>
                    <span class="file-name">{{ file.name }}</span>
                  </AppLink>
                </div>
              </div>
            </div>
          </div>

          <!-- 文件类型内容 -->
          <article v-else-if="props.article && props.article.type === 'file'" class="article-body markdown-body prose-neutral prose-sm md:prose-base prose max-w-none" ref="articleContentRef"  v-html="renderedContent"></article>
  

          <!-- 无法显示的内容 -->
          <div v-else class="article-body markdown-body">无法显示内容 或 类型未知</div>
        </template>

        <SkeletonItem class="article-body" v-else :rows="20"></SkeletonItem>

        <div class="article-footer" v-if="popularLinkList.length">
          <div class="popular-title">热点趋势</div>
          <div class="popular-body">
            <div class="popular-item" v-for="item in popularLinkList" :key="item.link">
              <AppLink :to="item.link">{{item.title}}</AppLink>
            </div>
          </div>
        </div>
    </div>

    <Toast ref="toastRef"/>
  </div>
</template>

<style>
 
</style>

<style scoped>


.article-body {
  background-color: #fff;
  overflow: hidden;
  margin: 1rem;

}

.article-footer {
  background-color: #fff;
  overflow: hidden;
  margin-top: 1rem;
  padding: .5rem;
  display: flex;
  flex-direction: column;
}

.popular-title {
  user-select: none;
  margin: .5rem 0 .5rem .5rem;
  font-size: 1.2rem;
  font-weight: bold;
}

.popular-body {
  display: inline-grid;
  grid-template-columns: repeat(3, 1fr);
  grid-gap: .25rem 1rem;
  margin: .5rem;
}

.popular-body .popular-item {
  font-size: .75rem;
  color: #666;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}

.popular-item {
  margin-bottom: .5rem;
}

.popular-body .popular-item a{
  color: #666;
}

.popular-body .popular-item:hover, .popular-body .popular-item:hover a {
  color: #06c;
  text-decoration: none;
}


/* 添加目录列表样式 */
.directory-listing {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.directory-section, .file-section {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 0.5rem;
}

.directory-item, .file-item {
  padding: 0.5rem;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.directory-item:hover, .file-item:hover {
  background-color: #f0f0f0;
}

.directory-link, .file-link {
  display: flex;
  align-items: center;
  color: #333;
  text-decoration: none;
}


.directory-link:hover, .file-link:hover {
  text-decoration: none;
}


.directory-icon, .file-icon {
  margin-right: 0.5rem;
  font-size: 1.2rem;
}

.directory-name, .file-name {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

:deep(.code-block-container) {
  position: relative;
}

:deep(.copy-btn) {
  position: absolute;
  top: .5rem;
  right: .5rem;
  padding: 0.25rem;
  background-color: rgba(255, 255, 255, 0.15);
  border: none;
  border-radius: 4px;
  cursor: pointer;
  z-index: 10;
  opacity: 0.6;
  transition: opacity 0.2s ease;
}

:deep(.code-block-container:hover .copy-btn) {
  opacity: 1;
}

:deep(.copy-btn:hover) {
  background-color: rgba(255, 255, 255, 0.2);
}

:deep(.copy-btn.copied) {
  background-color: rgba(46, 160, 67, 0.2);
}
</style>
