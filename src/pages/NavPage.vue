<template>

  <!-- Header: Correctly made sticky to the top of the viewport -->
  <header v-if="scrolled" class="fixed top-0 left-0 right-0 z-60 backdrop-blur-lg bg-white/50">
    <!-- ======================= START OF FIX 1: Header Responsive Tweaks ======================= -->
    <div class="flex items-center justify-end h-12 md:h-15 w-full select-none mx-4 ">
      <div class="flex items-center ">
        <i class="hidden md:fas md:fa-compass text-blue-600 text-2xl"></i>
        <h1 class="text-sm font-bold text-blue-600 xl:text-xl pl-2">LinuxDo <span class="hidden xl:inline-block">工具</span>导航</h1>
        <div class="hidden lg:flex items-center gap-4 ml-4 text-sm text-gray-600">
            <p class="hidden xl:block">NavHub - Linux Do Awesome Tools</p>
            <a href="#" @click.prevent="showContributionInfo" title="登录账号即可新增/删除书签" class="text-blue-600 hover:decoration-none">诚邀共建</a>
            <a href="mailto:<EMAIL>" title="发送邮件" class="text-blue-600 hover:decoration-none">意见反馈</a>
        </div>
      </div>
      <div class="flex items-center justify-end flex-auto mx-8">
        <!-- Search: Adjusted width for better mobile view -->
        <div class="relative w-40 sm:w-full sm:max-w-xs transition-all">
          <i class="fas fa-search absolute left-3 top-1/2 -translate-y-1/2 text-gray-400"></i>
          <input type="text" v-model="searchQuery" placeholder="搜索工具..." autocomplete="off" spellcheck="false" class="w-full pl-9 pr-4 py-2 border border-transparent transition-all outline-none bg-transparent focus:bg-gray-200/50 text-sm text-gray-800 placeholder-gray-400" />
        </div>
        <!-- Contribute Button: Hide text on extra-small screens -->
        <div v-if="userData">
          <button @click="showContributeDialog = true" class="mx-2 p-2 text-sm font-medium break-keep cursor-pointer text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition flex items-center ">
            <i class="fas fa-plus-circle sm:mr-1"></i>
            <span class="hidden sm:inline">提交项目</span>
          </button>
        </div>
      </div>
    </div>
  </header>
  <BodyLayout :spacing="false">
    <!-- Main Container: Simple flex-col. The body will scroll. -->
    <div class="flex flex-auto flex-col">
      <!-- Main Content Wrapper -->
      <div class="flex flex-auto">
        <!-- Sidebar: Sticks relative to the header -->
        <aside class="flex-shrink-0 hidden md:block w-48 lg:w-52 sticky top-15 h-50">
          <nav class="p-4 space-y-1">
            <a
              v-for="filter in categoriesForNav"
              :key="filter.value"
              href="#"
              @click.prevent="scrollToCategory(filter.value)"
              :class="['group flex items-center px-3 py-2 text-sm font-medium transition-colors duration-150', { 'bg-blue-50 text-blue-700 font-semibold': activeCategory === filter.value, 'text-gray-600 hover:bg-blue-50 hover:text-blue-700': activeCategory !== filter.value }]"
            >
              {{ filter.label }}
            </a>
          </nav>
        </aside>

        <!-- Main Content Area -->
        <main class="flex-auto min-w-0">
    
        

          <div class="flex flex-col flex-auto relative mt-2">
            <!-- All of your content sections, cards, and styling are preserved -->
            <div v-if="isLoading && groupedItems.length === 0"> <SkeletonItem :rows="50" /> </div>
            <div v-else-if="groupedItems.length === 0 && !errorLoadingItems" class="flex flex-col items-center justify-center h-96 text-center">
              <i class="fas fa-box-open text-5xl text-gray-300 mb-4"></i>
              <p class="text-gray-500">
                {{ searchQuery ? '未找到匹配的项目。' : '暂无导航项目，您可以尝试' }}
                <a href="#" @click.prevent="isLoggedIn ? (showContributeDialog = true) : handleLogin()" class="text-blue-600 hover:underline font-semibold">
                  {{ isLoggedIn ? '共建' : '登录后共建' }}
                </a>。
              </p>
            </div>
            
           

            <section
              v-for="group in groupedItems"
              :key="group.value"
              :id="`category-${group.value}`"
              data-category-section
            >
            
              <h2 class="tab-label sticky top-14 md:top-17 z-30 " >
                {{ group.label }}
              </h2>
              <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-4 select-none p-4">
                <div
                  v-for="item in group.items"
                  :key="item.id"
                  class="p-4 bg-blue-100/30 hover:-translate-y-[-1px] transition-all duration-300 cursor-pointer h-[130px] flex flex-col justify-between "
                  :data-topic-id="item.id"
                  :draggable="isLoggedIn"
                  :title="item.url"
                  @dragstart="handleDragStart(item, $event)"
                  @dragend="handleDragEnd"
                  @click="openItemUrl(item.url)"
                >
                  <div class="flex items-start">
                    <div class="w-10 h-10 bg-blue-100/50 text-blue-600 rounded-md flex items-center justify-center mr-3 flex-shrink-0">
                      <i :class="['fas', item.icon, 'text-lg']"></i>
                    </div>
                    <div class="overflow-hidden">
                      <h3 class="text-sm font-semibold text-gray-800 mb-1 whitespace-nowrap overflow-hidden text-ellipsis">
                        {{ item.title }}
                      </h3>
                      <p class="text-xs text-gray-500 leading-snug line-clamp-2">
                        {{ item.description }}
                      </p>
                    </div>
                  </div>
                  <div class="flex justify-between items-center">
                    <span class="text-xs font-medium text-blue-600 bg-blue-100/50 px-2 py-1 rounded">
                      {{ getCategoryDisplayName(item.category) }}
                    </span>
                    <a
                        v-if="item.topicUrl && item.topicUrl.trim()"
                        href="#"
                        @click.stop.prevent="openItemUrl(item.topicUrl)"
                        class="text-xs text-blue-600 hover:text-blue-700 transition-colors flex items-center gap-1"
                        :title="`${item.topicUrl}`"
                      >
                      <i class="fas fa-external-link-alt"></i> 
                      <span class="hidden xl:inline">直达链接</span>
                    </a>
                  </div>
                </div>
              </div>
            </section>
          </div>
        </main>
      </div>
    </div>

    <!-- [REFACTORED] Contribute Dialog with Tailwind CSS -->
    <div v-if="showContributeDialog"
        class="fixed inset-0 bg-black/50 flex items-center justify-center z-[1000] transition-opacity duration-300">
      <div class="bg-white rounded-lg shadow-xl w-[90%] max-w-sm p-5">
        <div class="flex justify-between items-center mb-4">
          <h3 class="text-lg font-semibold text-gray-800">推荐项目</h3>
          <button @click="showContributeDialog = false"
                  class="bg-transparent border-none text-2xl cursor-pointer text-gray-400 leading-none p-0 hover:text-gray-700">
            ×
          </button>
        </div>
        <div class="mb-3">
          <input
              type="text"
              v-model="newTopicId"
              placeholder="请输入 Topic ID"
              :disabled="isSubmittingTopic"
              class="box-border w-full py-2 px-3 border border-gray-300 rounded-md mb-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-500/25 disabled:bg-gray-100 disabled:cursor-not-allowed">
          <div class="text-xs text-gray-500 leading-tight flex items-center gap-1.5">
            <i class="fas fa-info-circle"></i>
            <span>Linux Do Topic ID，如 https://linux.do/t/topic/187077 中的 187077</span>
          </div>
        </div>
        <div class="mt-5 text-right">
          <button
              @click="submitNewTopic"
              :disabled="isSubmittingTopic || !newTopicId.trim()"
              class="inline-flex items-center justify-center bg-blue-600 text-white border-none py-2 px-5 rounded-md cursor-pointer text-sm font-medium transition-colors duration-200 ease-in-out hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed">
            <i v-if="isSubmittingTopic" class="fas fa-spinner animate-spin mr-2"></i>
            <span>{{ isSubmittingTopic ? '提交中...' : '提交' }}</span>
          </button>
        </div>
      </div>
    </div>

    <!-- [REFACTORED] Trash Bin with Tailwind CSS -->
    <div
        :class="[
          'fixed right-14 bottom-14 w-16 h-16 rounded-full flex items-center justify-center text-white text-2xl shadow-lg z-[1000] transition-all duration-300 ease-in-out',
          showTrashBin ? 'opacity-90 visible scale-100' : 'opacity-0 invisible scale-80',
          isDragOverTrash ? 'scale-110 bg-red-600 shadow-xl opacity-100' : 'bg-red-500'
        ]"
        @dragover.prevent="handleDragOverTrash"
        @dragleave="handleDragLeaveTrash"
        @drop.prevent="handleDropOnTrash"
    >
      <i class="fas fa-trash-alt"></i>
    </div>

    <!-- Toast Container -->
    <div class="toast-container">
      <div v-for="toast in toasts" :key="toast.id" :class="['toast', `toast-${toast.type}`, { show: toast.show }]">
        <div class="toast-icon">
          <i :class="['fas', `fa-${toastIcon(toast.type)}`]"></i>
        </div>
        <div class="toast-content">{{ toast.message }}</div>
      </div>
    </div>
  </BodyLayout>
</template>
<script setup>
// The <script> section requires TWO small but CRITICAL changes to work with the browser scroll.
import { ref, onMounted, computed, inject, onBeforeUnmount, nextTick } from 'vue';
import { useStore } from 'vuex';
import { useHead } from '@vueuse/head';
import SkeletonItem from '../components/SkeletonItem.vue';
import BodyLayout from "../components/BodyLayout.vue";
import { mapState } from '../stateutil';

const { scrolled, scrollDirection } = mapState()

const http = inject("http");
const store = useStore();

useHead({
  title: 'LinuxDo工具导航 - 优质资源与工具集合',
  meta: [
    { name: 'description', content: 'LinuxDo工具导航是一个集合了开发工具、资源荟萃、福利羊毛和社区资源的导航平台。' },
    { name: 'keywords', content: 'LinuxDo,工具导航,开发工具,资源导航,Linux社区,技术资源,福利羊毛' },
  ],
});

// --- STATE ---
const navItems = ref([]);
const searchQuery = ref('');
const isLoading = ref(false);
const isLoadingInitial = ref(true);
const errorLoadingItems = ref(null);
const isLoggedIn = ref(!!store.state.user);
const userData = store.state.user;
const showContributeDialog = ref(false);
const newTopicId = ref('');
const isSubmittingTopic = ref(false);
const showTrashBin = ref(false);
const isDragOverTrash = ref(false);
let draggedItem = null;
const toasts = ref([]);
let toastIdCounter = 0;
const activeCategory = ref(null);
let observer = null;
let isScrollingProgrammatically = false;

// Removed `scrollContainer` ref, it's not needed.

const filters = [
  { label: '开发调试', value: 'develop' },
  { label: '资源荟萃', value: 'resource' },
  { label: '福利羊毛', value: 'welfare' },
  { label: '搞七捻三', value: 'gossip' },
];

const categoriesForNav = computed(() => filters);
const groupedItems = computed(() => {
  if (!navItems.value.length) return [];
  const query = searchQuery.value.toLowerCase().trim();
  return filters
    .map(category => {
      let items = navItems.value.filter(item => item.category === category.value);
      if (query) {
        items = items.filter(item =>
          item.title.toLowerCase().includes(query) ||
          item.description.toLowerCase().includes(query)
        );
      }
      return { ...category, items };
    })
    .filter(group => group.items.length > 0);
});

const getCategoryDisplayName = (categoryKey) => {
  return filters.find(f => f.value === categoryKey)?.label || categoryKey;
};

const fetchNavItems = async () => {
  isLoading.value = true;
  try {
    const response = await http.primary().get(`/nav/items`);
    navItems.value = response.data;
    if (navItems.value.length > 0) {
       activeCategory.value = groupedItems.value[0]?.value || null;
    }
    await nextTick();
    setupIntersectionObserver();
  } catch (e) {
    errorLoadingItems.value = e;
    showToast('加载项目失败', 'error');
  } finally {
    isLoading.value = false;
    isLoadingInitial.value = false;
  }
};

const openItemUrl = (url) => {
  if (url) window.open(url, '_blank', 'noopener');
};

const scrollToCategory = (categoryValue) => {
  const targetElement = document.getElementById(`category-${categoryValue}`);
  if (targetElement) {
    isScrollingProgrammatically = true;
    activeCategory.value = categoryValue;
    
    const headerOffset = 60; // 调整偏移量，考虑sticky header
    const elementPosition = targetElement.getBoundingClientRect().top;
    const offsetPosition = elementPosition + window.pageYOffset - headerOffset;
  
    // 尝试多种滚动方法确保兼容性
    try {
      window.scrollTo({
        top: offsetPosition,
        behavior: "smooth"
      });
    } catch (e) {
      // 降级方案：使用scrollIntoView
      targetElement.scrollIntoView({
        behavior: 'smooth',
        block: 'start'
      });
    }
    
    setTimeout(() => {
      isScrollingProgrammatically = false;
    }, 1000);
  }
};

const setupIntersectionObserver = () => {
  if (observer) observer.disconnect();

  // ======================= START SCRIPT FIX 2 =======================
  const options = {
    root: null, // <-- IMPORTANT: Use the browser viewport as the root.
    rootMargin: "-20% 0px -60% 0px",
    threshold: 0,
  };
  // ======================= END SCRIPT FIX 2 =======================

  observer = new IntersectionObserver((entries) => {
    if (isScrollingProgrammatically) return;
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        const categoryId = entry.target.id.replace('category-', '');
        activeCategory.value = categoryId;
      }
    });
  }, options);

  const sections = document.querySelectorAll('[data-category-section]');
  sections.forEach(section => observer.observe(section));
};

// ... (rest of the script is unchanged)
const submitNewTopic = async () => {
  if (!newTopicId.value.trim()) return showToast('请输入 Topic ID', 'error');
  isSubmittingTopic.value = true;
  try {
    await http.get(`/nav/add?topicId=${newTopicId.value.trim()}`);
    showToast('项目添加成功!', 'success');
    showContributeDialog.value = false;
    newTopicId.value = '';
    await fetchNavItems();
  } catch (error) {
    showToast(error.message, 'error');
  } finally {
    isSubmittingTopic.value = false;
  }
};

const showContributionInfo = () => {
  if (isLoggedIn.value) {
    showContributeDialog.value = true;
  } else {
    showToast('请先登录以共建书签栏', 'info');
  }
};
const handleDragStart = (item, event) => {
  if (!isLoggedIn.value) return;
  draggedItem = item;
  event.dataTransfer.setData('text/plain', item.id);
  event.dataTransfer.effectAllowed = 'move';
  showTrashBin.value = true;
};
const handleDragEnd = () => {
  if (!isLoggedIn.value) return;
  resetDragState();
};
const handleDragOverTrash = () => {
  if (!isLoggedIn.value) return;
  isDragOverTrash.value = true;
};
const handleDragLeaveTrash = () => {
  if (!isLoggedIn.value) return;
  isDragOverTrash.value = false;
};
const handleDropOnTrash = async (event) => {
  if (!isLoggedIn.value || !draggedItem) return;
  const topicId = event.dataTransfer.getData('text/plain') || draggedItem.id;
  if (!topicId) return showToast('无法获取项目ID', 'error');
  showToast('正在删除...', 'info');
  try {
    await http.delete(`/nav/items/${topicId}`);
    showToast('项目删除成功!', 'success');
    await fetchNavItems();
  } catch (error) {
    showToast(error.message, 'error');
  } finally {
    resetDragState();
  }
};
const resetDragState = () => {
  showTrashBin.value = false;
  isDragOverTrash.value = false;
  draggedItem = null;
};

const toastIcon = (type) => {
  const map = { success: 'check-circle', error: 'exclamation-circle', info: 'info-circle' };
  return map[type] || 'info-circle';
};

const showToast = (message, type = 'info', duration = 3000) => {
  const id = toastIdCounter++;
  const toast = {id, message, type, show: false};
  toasts.value.push(toast);

  // Trigger animation
  requestAnimationFrame(() => {
    const currentToast = toasts.value.find(t => t.id === id);
    if (currentToast) currentToast.show = true;
  });

  setTimeout(() => {
    const currentToast = toasts.value.find(t => t.id === id);
    if (currentToast) currentToast.show = false; // Start fade out
    setTimeout(() => { // Remove from array after fade out
      toasts.value = toasts.value.filter(t => t.id !== id);
    }, 300); // Match CSS transition duration
  }, duration);
};

onMounted(() => {
  fetchNavItems();
  window.addEventListener('resize', setupIntersectionObserver);
});
onBeforeUnmount(() => {
  if (observer) observer.disconnect();
  window.removeEventListener('resize', setupIntersectionObserver);
});
</script>
<style scoped>
  @import "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css";


  /* Toast Styles */
  .toast-container {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 2000; /* Above other elements */
    display: flex;
    flex-direction: column-reverse; /* New toasts appear on top */
    gap: 10px;
  }

  .toast {
    background-color: white;
    border-radius: 8px;
    padding: 12px 16px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    display: flex;
    align-items: center;
    gap: 12px;
    min-width: 280px;
    max-width: 400px;
    transform: translateX(100%); /* Start off-screen */
    opacity: 0;
    transition: transform 0.3s ease-out, opacity 0.3s ease-out;
    border-left: 4px solid transparent; /* Base for colored border */
  }

  .toast.show {
    transform: translateX(0);
    opacity: 1;
  }

  .toast-success { border-left-color: #28a745; background: linear-gradient(to right, #eaf6ec, white); }
  .toast-error   { border-left-color: #dc3545; background: linear-gradient(to right, #fdecea, white); }
  .toast-info    { border-left-color: #17a2b8; background: linear-gradient(to right, #e7f6f8, white); }


  .toast-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    flex-shrink: 0;
    font-size: 18px; /* Icon size */
  }
  .toast-success .toast-icon { color: #28a745; }
  .toast-error .toast-icon   { color: #dc3545; }
  .toast-info .toast-icon    { color: #17a2b8; }


  .toast-content {
    color: #333; /* Darker text */
    font-size: 14px;
    line-height: 1.5; /* Improved readability */
    flex: 1;
  }



  .tab-label {
    background-color: #2563eb; /* 蓝色背景 */
    opacity: .9;
    color: white;
    width: 100px;
    padding: .25rem 0 .25rem 1rem;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  }

  .tab-label::after {
    content: '';
    position: absolute;
    top: 0;
    right: -1rem; /* 箭头向右延伸 */
    width: 0;
    height: 0;
    border-left: 1rem solid #2563eb; /* 箭头颜色与背景一致 */
    border-top: 1rem solid transparent; /* 上边框透明，约为标签高度的一半 */
    border-bottom: 1rem solid transparent; /* 下边框透明，约为标签高度的一半 */
  }

</style>