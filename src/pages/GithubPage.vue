<script setup>
  import GithubSlave from "./GithubSlave.vue";
  import Article from "./Article.vue";
  import {onMounted} from "vue";
  import { useRoute, useRouter } from 'vue-router'
  import {inject, ref} from "vue"; // Remove computed
  import BodyLayout from "../components/BodyLayout.vue";
  import RecommendationList from "../components/RecommendationList.vue";
  import { mapState, mapMutations } from '../stateutil.js'; //
  
  const http = inject('http')

  const router = useRouter();
  const route = useRoute();

  const { previewLinkList } = mapState() 

  const article = ref({})
  const articleList = ref([])
  const fetching = ref(true)
  const headingsFromArticle = ref([]) // Ref to store headings from Article component
  const activeHeadingIdFromArticle = ref(null) // Ref to store active heading ID from Article component
  
  // Handlers for events from Article component
  const handleHeadingsUpdate = (headings) => {
    headingsFromArticle.value = headings;
  };
  const handleActiveHeadingUpdate = (id) => {
    activeHeadingIdFromArticle.value = id;
  };
  
  
  onMounted(async () => {
    const repo = ((route.params.repo === 'a') ? 'gitbook' : 'secretbook');
    http.get(`/github/${repo}/${route.params.key}`).then((resp) => {
      article.value = resp.data;

      if(article.value.type === 'dir') {
        articleList.value = article.value.entries.map(item => {
          return {
            url: `/${route.params.repo}/${item.path}`,
            title: item.name.lastIndexOf('.') === -1 ? item.name : item.name.slice(0, item.name.lastIndexOf('.'))
          }
        })
      }
    }).finally(() => {
      fetching.value = false
    });

  });
</script>

<template>
  <BodyLayout>
    <!-- Listen for events from Article -->
    <Article
      :article="article"
      :fetching="fetching"
      :key="article && article.type"
      @update:headings="handleHeadingsUpdate"
      @update:active-heading-id="handleActiveHeadingUpdate"
    ></Article>

    <!-- Add RecommendationList here, bind to previewLinkList from store -->
    <RecommendationList :recommendations="previewLinkList" />

    <template v-slot:slider>
      <!-- Pass headings and active ID to GithubSlave -->
      <GithubSlave
        :articleList="articleList"
        :headings="headingsFromArticle"
        :active-heading-id="activeHeadingIdFromArticle"
      />
    </template>
  </BodyLayout>
</template>

<style scoped>

</style>
