<template>
  <div class="footer">
    <div class="footer-item">
      <span class="small" :title="indexQuoteData.last_time">等权指数</span>{{ indexQuoteData.cur_increase_rt }}% 
      <span class="small">成交量</span>{{ indexQuoteData.volume }}亿 
    </div>
    <div class="footer-item">
      <span class="small">中位数</span>{{ indexQuoteData.mid_price }} 
      <span class="small">中位溢价率</span>{{ indexQuoteData.mid_premium_rt }}%
    </div>
    <div class="footer-item">
      <span class="small">平均数</span>{{ indexQuoteData.avg_price }} 
      <span class="small">平均溢价率</span>{{ indexQuoteData.avg_premium_rt }}% 
      <span class="small">平均到期收益率</span>{{ indexQuoteData.avg_ytm_rt }}%
    </div>
    <div class="footer-item">
      <span class="small">小于90({{ indexQuoteData.price_90 }})</span>{{ indexQuoteData.increase_rt_90 }}% 
    </div>
    <div class="footer-item">
      <span class="small">90-100元({{ indexQuoteData.price_90_100 }})</span>{{ indexQuoteData.increase_rt_90_100 }}%
    </div>
    <div class="footer-item">
      <span class="small">100-110元({{ indexQuoteData.price_100_110 }})</span>{{ indexQuoteData.increase_rt_100_110 }}%
    </div>
    <div class="footer-item">
      <span class="small">110-120元({{ indexQuoteData.price_110_120 }})</span>{{ indexQuoteData.increase_rt_110_120 }}%
    </div>
    <div class="footer-item">
      <span class="small">120-130元({{ indexQuoteData.price_120_130 }})</span>{{ indexQuoteData.increase_rt_120_130 }}%
    </div>
    <div class="footer-item"> 
      <span class="small">大于130元({{ indexQuoteData.price_130 }})</span>{{ indexQuoteData.increase_rt_130 }}% 
    </div>
  </div>
</template>

<script>
export default {
  name: 'StockFooter',
  props: {
    indexQuoteData: {
      type: Object,
      required: true
    }
  }
}
</script>

<style scoped>
.footer {
  background-color: #fff;
  padding: .5rem 1rem;
  display: flex;
}

.footer-item {
  padding-right: 1rem;
}

.footer-item span {
  padding-right: .25rem;
}

.small {
  font-size: .75rem;
}
</style>
