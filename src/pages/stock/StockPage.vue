<template>
  <div id="app" class="stock-page">
    <StockHeader
      :tab="tab"
      :panel="panel"
      :insearch="insearch"
      :progress="progress"
      :filter-condition="filterCondition"
      :price-tag-list="priceTagList"
      :price-tag="priceTag"
      :tag-name-list="tagNameList"
      :tag-name="tagName"
      :current-cookie="currentCookie"
      :selected-tag-class="selectedTagClass"
      @select-tab="selectTab"
      @toggle-panel="togglePanel"
      @update-insearch="updateInsearch"
      @select-price-tag="selectPriceTag"
      @selected-tag="selectedTag"
      @toggle-tag="toggleTag"
      @record-price="recordPrice"
      @save-cookie="saveCookie"
      @refresh-cookie="refreshCookie"
      @update-extra="updateExtra"
      @update:tagName="tagName = $event"
      @update:priceTag="priceTag = $event"
      @update:filterCondition="filterCondition = $event"
      @update:currentCookie="currentCookie = $event"
    />

    <StockFooter v-if="indexQuoteData" :index-quote-data="indexQuoteData" />

    <StockList
      :tab="tab"
      :stock-list="stockList"
      :price-tag="priceTag"
      :marked-new-map="markedNewMap"
      :loading="loading"
      :load-error="loadError"
      @sort-by="sortBy"
      @marked-new="markedNew"
      :progress-tip="progressTip"
      :down-tip="downTip"
      :force-tip="forceTip"
      :resold-tip="resoldTip"
    />
  </div>
</template>

<script>
import { ref, onBeforeMount, computed } from 'vue'
import axios from 'axios'
import StockHeader from './StockHeader.vue'
import StockFooter from './StockFooter.vue'
import StockList from './StockList.vue'

// Stock页面专用的click-outside指令
const clickOutside = {
  beforeMount: (el, binding) => {
    el.clickOutsideEvent = event => {
      if (!(el == event.target || el.contains(event.target))) {
        binding.value();
      }
    };
    document.addEventListener("click", el.clickOutsideEvent);
  },
  unmounted: el => {
    document.removeEventListener("click", el.clickOutsideEvent);
  },
};

export default {
  name: 'StockPage',
  components: {
    StockHeader,
    StockFooter,
    StockList
  },
  directives: {
    'click-outside': clickOutside
  },
  setup() {
    const tab = ref('now')
    const newList = ref([])
    const nowList = ref([])
    const panel = ref('') // 过滤filter 设置setting
    const sortCol = ref('')
    const revertSort = ref(false)
    const indexQuoteData = ref(null)
    const progress = ref('')
    const markedNewMap = ref({})
    const loading = ref(false)
    const loadError = ref(false)
    const filterCondition = ref({
      minYtmRt: -3, //到期收益率
      maxYearLeft: 3, //久期
      maxPrice: 130, //价格
      maxCurrIssAmt: 5,//规模
      maxPremiumRt: 100, //溢价率
      minRatingCd: 'A',//最低评级
      identify: ''
    })

    const tagMap = ref({})
    const tagName = ref('')

    const selectedTagNameList = ref(new Set())
    const insearch = ref(false)
    const priceTag = ref('')
    const priceMap = ref({})
    const currentCookie = ref(document.cookie)


    // URL参数处理
    let uri = window.location.search.substring(1);
    let params = new URLSearchParams(uri);
    if (params.get("tab")) {
      tab.value = params.get("tab")
    }

    if (params.get("sort")) {
      sortCol.value = params.get("sort")
    }

    if (params.get("asc")) {
      revertSort.value = true
    }

    const login = async () => {
      if (document.cookie.indexOf('kbzw__user_login') === -1) {
        let response = await axios.post('https://stock.lan/api/webapi/account/login_process/', new URLSearchParams({
          "return_url": "https://www.jisilu.cn/",
          "user_name": "78ddb71b186664a25ad38ce1a5f05bc6",
          "password": "adf3890e1781ae5cac2359da898afd20",
          "auto_login": "1",
          "aes": "1"
        }))
      }
    }

    //可转债等权期指
    const updatePanel = async () => {
      let response = await axios.get('https://stock.lan/api/webapi/cb/index_quote/')
      indexQuoteData.value = response.data.data
    }

    const updateListExtra = (dataList) => {
      let downList = localStorage.getItem("downList");
      let downMap = {}
      if (downList && downList.length) {
        downList = JSON.parse(downList)
        downList.forEach((item, i) => {
          downMap[item.bondNo] = item
        })
      }

      let forceList = localStorage.getItem("forceList");
      let forceMap = {}
      if (forceList && forceList.length) {
        forceList = JSON.parse(forceList)
        forceList.forEach((item, i) => {
          forceMap[item.bondNo] = item
        })
      }

      let resoldList = localStorage.getItem("resoldList");
      let resoldMap = {}
      if (resoldList && resoldList.length) {
        resoldList = JSON.parse(resoldList)
        resoldList.forEach((item, i) => {
          resoldMap[item.bondNo] = item
        })
      }

      nowList.value = dataList.map(item => {
        if (downMap[item.bond_id]) {
          item = Object.assign(item, downMap[item.bond_id]);
        }
        if (forceMap[item.bond_id]) {
          item = Object.assign(item, forceMap[item.bond_id]);
        }
        if (resoldMap[item.bond_id]) {
          item = Object.assign(item, resoldMap[item.bond_id]);
        }

        if (tagMap.value) {
          let tagNameList = [];
          for (let tagName in tagMap.value) {
            if (tagMap.value[tagName].indexOf(item.bond_id) !== -1) {
              tagNameList.push(tagName)
            }
          }

          item.tagNameList = tagNameList;
        }

        if (priceMap.value && priceTag.value && priceMap.value[priceTag.value]) {
          let recordStock = priceMap.value[priceTag.value].find(stock => stock.bond_id === item.bond_id)
          if (recordStock) {
            item.record_price = recordStock.price
            item.record_diff_rate = parseFloat(((item.price - item.record_price) / item.record_price * 100).toFixed(2))
          }
        } else {
          item.record_price = null
          item.record_diff_rate = null
        }
        return item;
      })
    }

    const updateNewListExtra = (dataList) => {
      let targetMap = localStorage.getItem("markedNewMap")
      if (targetMap) {
        targetMap = JSON.parse(targetMap)
        markedNewMap.value = targetMap
      }

      newList.value = dataList.map(item => {
        if (markedNewMap.value[item.stock_id]) {
          item.target_price = markedNewMap.value[item.stock_id]
          item.diff_price = (item.price - markedNewMap.value[item.stock_id]).toFixed(2)
          item.diff_rate = Math.abs(parseFloat((item.diff_price / item.price * 100).toFixed(2)))
        }
        else {
          item.target_price = null
          item.diff_price = null
          item.diff_rate = null
        }
        return item;
      })
    }

    const updateList = async () => {
      loading.value = true
      loadError.value = false

      try {
        if (tab.value == 'new') {
          let response = await axios.get('https://stock.lan/api/webapi/cb/pre/?history=N')
          updateNewListExtra(response.data.data)
        }
        else {
          login()
          updatePanel()
          let response = await axios.get('/api/webapi/cb/list/', {
            headers: {
              'Init': '1',
              'Columns': '1,70,2,3,5,6,11,12,14,15,16,29,30,32,34,44,46,47,50,52,53,54,56,57,58,59,60,62,63,67'
            }
          })

          updateListExtra(response.data.data);
        }
      } catch (error) {
        console.error('Failed to load stock data:', error)
        loadError.value = true
      } finally {
        loading.value = false
      }
    }

    //下修
    const updateExtra = async () => {
      if (progress.value) {
        return;
      }
      progress.value = "下修"
      let response = await axios.get('/bond/downList', {
        headers: {
          'Authorization': 'Bearer 9441389MDQ6VXNlcjk0NDEzODk='
        }
      })
      let downList = response.data.data
      try {
        downList = JSON.stringify(downList);
        JSON.parse(downList)
        localStorage.setItem("downList", downList)
      } catch (e) {
        console.error("downlist获取失败")
      }

      progress.value = "强赎"

      response = await axios.get('/bond/forceList', {
        headers: {
          'Authorization': 'Bearer 9441389MDQ6VXNlcjk0NDEzODk='
        }
      })
      let forceList = response.data.data
      try {
        forceList = JSON.stringify(forceList);
        JSON.parse(forceList)
        localStorage.setItem("forceList", forceList)
      } catch (e) {
        console.error("forceList获取失败")
      }

      progress.value = "回售"

      response = await axios.get('/bond/resoldList', {
        headers: {
          'Authorization': 'Bearer 9441389MDQ6VXNlcjk0NDEzODk='
        }
      })
      let resoldList = response.data.data

      try {
        resoldList = JSON.stringify(resoldList);
        JSON.parse(resoldList)
        localStorage.setItem("resoldList", resoldList)
      } catch (e) {
        console.error("resoldList获取失败")
      }

      updateListExtra(nowList.value)
      progress.value = ''
    }

    const filtering = (item) => {
      if (panel.value === 'filter') {
        let condition = filterCondition.value
        if (condition.identify) {
          let identifyList = condition.identify.split('\n').filter(element => element);
          return identifyList.filter((identify) => item.bond_id.indexOf(identify) !== -1 || item.bond_nm.indexOf(identify) !== -1).length > 0;
        }
        return item.rating_cd.startsWith(condition.minRatingCd) && item.year_left <= condition.maxYearLeft && item.year_left > condition.maxYearLeft - 1 && item.ytm_rt >= condition.minYtmRt && item.price <= condition.maxPrice && item.curr_iss_amt <= condition.maxCurrIssAmt && item.premium_rt <= condition.maxPremiumRt;
      }
      return true;
    }

    const sorting = (first, second) => {
      if (sortCol.value) {
        if (first[sortCol.value] === null || first[sortCol.value] === undefined) {
          return revertSort.value ? 1 : -1;
        }

        if (second[sortCol.value] === null || second[sortCol.value] === undefined) {
          return revertSort.value ? -1 : 1;
        }

        if (first[sortCol.value] > second[sortCol.value]) {
          return revertSort.value ? 1 : -1;
        }

        return revertSort.value ? -1 : 1;
      }

      return 0;
    }

    onBeforeMount(async () => {
      updateList()
      let targetMap = localStorage.getItem("priceMap")
      if (targetMap) {
        targetMap = JSON.parse(targetMap)
        priceMap.value = targetMap
      }

      targetMap = localStorage.getItem("tagMap")
      if (targetMap) {
        targetMap = JSON.parse(targetMap)
        tagMap.value = targetMap
      }
    })

    const recordPrice = (target) => {
      if (priceMap.value[target]) {
        delete priceMap.value[target];
        priceTag.value = "";
      }
      else {
        priceMap.value[target] = nowList.value;
      }

      localStorage.setItem("priceMap", JSON.stringify(priceMap.value));
      updateListExtra(nowList.value)
    }

    const selectPriceTag = (tagValue) => {
      if (priceTag.value == tagValue) {
        priceTag.value = ''
      }
      else {
        priceTag.value = tagValue;
      }

      updateListExtra(nowList.value)
    }

    const updateInsearch = (value) => {
      insearch.value = value;
      console.log(insearch.value)
    }

    const priceTagList = computed(() => {
      return Object.keys(priceMap.value)
    })

    const tagNameList = computed(() => {
      return Object.keys(tagMap.value)
    })

    const selectedTagClass = (tagName) => {
      return selectedTagNameList.value.has(tagName) ? 'marked' : '';
    }

    const selectedTag = (tagName) => {
      if (selectedTagNameList.value.has(tagName)) {
        selectedTagNameList.value.delete(tagName)
      }
      else {
        selectedTagNameList.value.add(tagName)
      }

      filterCondition.value.identify = ''

      let bondIds = new Set()
      selectedTagNameList.value.forEach((tagName, i) => {
        if (tagMap.value[tagName]) {
          tagMap.value[tagName].forEach((bondId, j) => {
            bondIds.add(bondId)
          })
        }
      });

      filterCondition.value.identify = [...bondIds].join('\n')
    }

    const toggleTag = (tagName) => {
      if (!tagMap.value[tagName]) {
        tagMap.value[tagName] = []
      }

      let tagList = stockList.value.map(stock => stock.bond_id).filter(bondId => tagMap.value[tagName].indexOf(bondId) === -1);

      if (tagList && tagList.length > 0) {
        tagMap.value[tagName].push(...tagList)
      }
      else {
        delete tagMap.value[tagName]
      }

      localStorage.setItem("tagMap", JSON.stringify(tagMap.value));

      updateListExtra(nowList.value)
    }

    const progressTip = (stock) => {
      let tip = ""
      if (stock.forceStatus) {
        if (stock.forceStatus.indexOf("强赎") === -1) {
          tip += "[强赎]"
        }
        tip += stock.forceStatus + " "
      }

      if (stock.forceCount < 10) {
        tip += "[强赎]需满足" + stock.forceCount + "日 "
      }

      if (stock.downStatus) {
        if (stock.downStatus.indexOf("下修") === -1) {
          tip += "[下修]"
        }
        tip += stock.downStatus + " "
      }

      if (stock.downCount < 10) {
        tip += "[下修]需满足" + stock.downCount + "日 "
      }

      if (stock.resoldStatus) {
        if (stock.resoldStatus.indexOf("回售") === -1) {
          tip += "[回售]"
        }
        tip += stock.resoldStatus + " "
      }

      return tip;
    }

    const downTip = (stock) => {
      if (stock.downStatus) {
        let tip = stock.downStatus;

        if (stock.notDownEndDate) {
          let diff = new Date(stock.notDownEndDate).getTime() - Date.now()
          let diffDay = Math.ceil(diff / 86400000)
          if (diffDay > 0) {
            tip += "\n" + diffDay + "天内不下修"
          }

          tip += "\n" + stock.forceTip.substr(22)
        }

        return tip;
      }

      return "下修比例:" + stock.downRate + "\n" + "需要满足" + stock.downCount + "天";
    }

    const forceTip = (stock) => {
      if (stock.forceStatus) {
        let tip = stock.forceStatus;

        if (stock.notForceEndTime) {
          let diff = new Date(stock.notForceEndTime).getTime() - Date.now()
          let diffDay = Math.ceil(diff / 86400000)
          if (diffDay > 0) {
            tip += "\n" + diffDay + "天内不强赎"
          }
        }
        else {
          tip += "\n" + (stock.forceTip && stock.forceTip.substr("22"))
        }

        return tip;
      }

      return "强赎比例:" + stock.forceRate + "\n" + "需要满足" + stock.forceCount + "天";
    }

    const resoldTip = (stock) => {
      let tip = ""
      if (stock.resoldStatus) {
        tip = stock.resoldStatus;
      }
      else {
        tip = "回售比例:" + stock.resoldRate;
      }

      let diff = new Date(stock.resoldStartTime).getTime() - Date.now()
      let diffDay = Math.floor(diff / 86400000)
      if (diffDay > 0) {
        tip += "\n" + diffDay + "天后可回售"
      }
      else {
        tip += "\n已到回售期"
      }

      tip += "\n" + (stock.resoldTip && stock.resoldTip.substr("22"))

      return tip;
    }

    const sortBy = (col) => {
      if (sortCol.value == col) {
        revertSort.value = !revertSort.value
      }
      else {
        revertSort.value = false
      }
      sortCol.value = col
    }

    const selectTab = (tabValue) => {
      tab.value = tabValue
      updateList()
    }

    const togglePanel = (targetPanel) => {
      if (panel.value === targetPanel) {
        panel.value = ''
      }
      else {
        panel.value = targetPanel
      }
    }

    const markedNew = (stock, price) => {
      if (!price) {
        delete markedNewMap.value[stock.stock_id]
      } else {
        markedNewMap.value[stock.stock_id] = price
      }

      localStorage.setItem("markedNewMap", JSON.stringify(markedNewMap.value))
      updateNewListExtra(newList.value)
    }

    const stockList = computed(() => {
      if (tab.value == 'new') {
        return newList.value.filter(item => { return !("" + item.stock_id).startsWith('688') }).sort(sorting)
      }
      return nowList.value.filter(item => { return !item.bond_id.startsWith(40) && item.price_tips !== '待上市' }).filter(filtering).sort(sorting)
    })

    const refreshCookie = () => {
      currentCookie.value = document.cookie
    }

    const saveCookie = () => {
      try {
        // 解析cookie字符串并设置每个cookie
        const cookies = currentCookie.value.split(';')
        cookies.forEach(cookie => {
          const trimmedCookie = cookie.trim()
          if (trimmedCookie) {
            // 直接设置cookie，浏览器会自动处理
            document.cookie = trimmedCookie
          }
        })
        alert('Cookie保存成功！')
      } catch (error) {
        alert('Cookie保存失败：' + error.message)
      }
    }

    return {
      tab,
      panel,
      sortCol,
      indexQuoteData,
      filterCondition,
      updateExtra,
      progress,
      markedNewMap,
      insearch,
      priceTag,
      currentCookie,
      recordPrice,
      selectPriceTag,
      updateInsearch,
      priceTagList,
      tagNameList,
      selectedTagClass,
      selectedTag,
      toggleTag,
      progressTip,
      downTip,
      forceTip,
      resoldTip,
      sortBy,
      selectTab,
      togglePanel,
      markedNew,
      stockList,
      refreshCookie,
      saveCookie,
      tagName,
      loading,
      loadError
    }
  }
}
</script>

<style scoped>
/* Stock页面专用样式 */
.stock-page {
  margin: 0;
  padding: 0;
}

.stock-page .small {
  font-size: .75rem;
}

.stock-page .marked {
  background-color: hsl(46, 100%, 91%);
}

.stock-page .tag-item {
  padding: .25rem;
  border-radius: .5rem;
  margin-right: .25rem;
}

.stock-page .selectable {
  user-select: all;
}

.stock-page .stock-red {
  color: red;
}

.stock-page .stock-green {
  color: green;
}

.stock-page .stock-grey {
  color: grey;
}

.stock-page .stock-flg {
  font-size: 12px;
  margin-left: 0.25rem;
  user-select: none;
  vertical-align: top;
}

.stock-page .stock-price {
  padding: 0 .25rem;
}

.stock-page .stock-price[tip]:not([tip=""]) {
  color: #06c;
  cursor: pointer;
}

.stock-page .stock-item[title]:not([title=""]) {
  color: #06c;
  cursor: pointer;
}

.stock-page input[type="text"] {
  box-sizing: border-box;
  outline: none;
  border: 0;
  margin: 0 .25rem;
  width: 3rem;
  font-size: 1rem;
}

.stock-page input[type="range"] {
  -webkit-appearance: none;
  appearance: none;
  background: transparent;
  cursor: pointer;
  width: 8rem;
}

.stock-page input[type="range"]:focus {
  outline: none;
}

.stock-page input[type="range"]::-webkit-slider-runnable-track {
  background-color: hsl(27, 87%, 87%);
  border-radius: 0.5rem;
  height: .5rem;
}

.stock-page input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  margin-top: -2px;
  background-color: hsl(23, 87%, 97%);
  border-radius: 0.5rem;
  height: .75rem;
  width: 1rem;
}

.stock-page textarea {
  border: solid 1px hsl(46, 100%, 91%);
  overflow: auto;
  outline: none;
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  box-shadow: none;
  resize: none;
  margin-left: 0.25rem;
  margin-top: 0.25rem;
}
</style>
