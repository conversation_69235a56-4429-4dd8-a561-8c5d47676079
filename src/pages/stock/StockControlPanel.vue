<template>
  <div class="header" :class="insearch ? 'focus' : ''">
    <div class="header-item" :class="tab == 'new' ? 'curr' : ''" @click="$emit('select-tab', 'new')">待发债</div>
    <div class="header-item" :class="tab == 'now' ? 'curr' : ''" @click="$emit('select-tab', 'now')">可转债</div>

    <div class="header-item search-btn" v-if="tab == 'now'" :class="panel == 'filter' && 'active'">
      <div @click="$emit('toggle-panel', 'filter')">选债</div>
      <div class="header-box" v-if="panel == 'filter'">
        <div class="header-box-wrap" @click="$emit('update-insearch', true)" v-click-outside="() => $emit('update-insearch', false)">
          <div class="header-box-left">
            <div class="header-box-item">
              <span class="small search-title"> 收益</span>
              <input type="range" min="-10" max="10" :value="filterCondition.minYtmRt" @input="$emit('update:filterCondition', {...filterCondition, minYtmRt: $event.target.value})" />
              <span class="small range-tip">{{ filterCondition.minYtmRt }}%</span>
            </div>
            <div class="header-box-item">
              <span class="small search-title"> 溢价</span>
              <input type="range" min="0" max="200" :value="filterCondition.maxPremiumRt" @input="$emit('update:filterCondition', {...filterCondition, maxPremiumRt: $event.target.value})" />
              <span class="small range-tip">{{ filterCondition.maxPremiumRt }}%</span>
            </div>
            <div class="header-box-item">
              <span class="small search-title"> 久期</span>
              <input type="range" min="1" max="5" :value="filterCondition.maxYearLeft" @input="$emit('update:filterCondition', {...filterCondition, maxYearLeft: $event.target.value})" />
              <span class="small range-tip">{{ filterCondition.maxYearLeft }}年</span>
            </div>
            <div class="header-box-item">
              <span class="small search-title"> 限价</span>
              <input type="range" min="100" max="200" :value="filterCondition.maxPrice" @input="$emit('update:filterCondition', {...filterCondition, maxPrice: $event.target.value})" />
              <span class="small range-tip">{{ filterCondition.maxPrice }}元</span>
            </div>
            <div class="header-box-item">
              <span class="small search-title"> 余额</span>
              <input type="range" min="1" max="20" :value="filterCondition.maxCurrIssAmt" @input="$emit('update:filterCondition', {...filterCondition, maxCurrIssAmt: $event.target.value})" />
              <span class="small range-tip">{{ filterCondition.maxCurrIssAmt }}亿</span>
            </div>

            <div class="header-box-item item-footer">
              <span class="tag-item small" v-for="tag in priceTagList" :key="tag"
                v-on:click="$emit('select-price-tag', tag)"
                :class="priceTag == tag ? 'marked' : ''">{{ tag }}</span>
            </div>
          </div>

          <div class="header-box-right">
            <div class="header-box-item item-main">
              <textarea cols="30" rows="5" :value="filterCondition.identify" @input="$emit('update:filterCondition', {...filterCondition, identify: $event.target.value})" placeholder="筛选多行"
                autofocus></textarea>
            </div>
            <div class="header-box-item item-footer">
              <div class="item-footer">
                <input type="text" :value="tagName" @input="$emit('update:tagName', $event.target.value)" placeholder="标签名" />
                <span class="small search-title" @click="$emit('toggle-tag', tagName)"> 标记 </span>
              </div>
              <div class="item-footer">
                <input type="text" :value="priceTag" @input="$emit('update:priceTag', $event.target.value)" placeholder="收盘记录" />
                <span class="small search-title" @click="$emit('record-price', priceTag)"> 记录 </span>
              </div>
            </div>
            <div class="header-box-item item-footer">
            </div>

            <div class="header-box-item item-footer">
              <span class="tag-item small" v-for="name in tagNameList" :key="name" v-on:click="$emit('selected-tag', name)"
                :class="selectedTagClass(name)">{{ name }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="header-item search-btn" v-if="tab == 'now'" :class="tab == 'now' && panel == 'setting' && 'active'">
      <div @click="$emit('toggle-panel', 'setting')">{{ progress || '设置' }}</div>
      <div class="header-box" v-if="panel == 'setting'">
        <div class="header-box-wrap cookie-panel" @click="$emit('update-insearch', true)" v-click-outside="() => $emit('update-insearch', false)">
          <div class="cookie-panel-content">
            <div class="header-box-item">
              <span class="small ">宁稳网cookies(数据仅保存本地):</span>
            </div>
            <div class="header-box-item item-main">
              <textarea cols="50" rows="8" :value="currentCookie" @input="$emit('update:currentCookie', $event.target.value)"
                placeholder="Cookie内容将在这里显示..."></textarea>
            </div>
            <div class="header-box-item item-footer">
              <div class="item-footer">
                <span class="small" @click="$emit('save-cookie')"> 保存session </span>
                <span class="small"
                      :class="{ 'disabled': progress }"
                      @click="progress ? null : $emit('update-extra')">
                  {{ progress || '拉取扩展数据' }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

     <!-- 置顶按钮 -->
    <div v-if="scrolled" class="header-item top-btn" @click="scrollToTop" title="回到顶部">
      <div class="top-icon">顶部</div>
    </div>

  </div>
</template>

<script>
import { mapState } from '../../stateutil.js';

// Stock页面专用的click-outside指令
const clickOutside = {
  beforeMount: (el, binding) => {
    el.clickOutsideEvent = event => {
      if (!(el == event.target || el.contains(event.target))) {
        binding.value();
      }
    };
    document.addEventListener("click", el.clickOutsideEvent);
  },
  unmounted: el => {
    document.removeEventListener("click", el.clickOutsideEvent);
  },
};




export default {
  name: 'StockControlPanel',
  props: {
    tab: String,
    panel: String,
    insearch: Boolean,
    progress: String,
    filterCondition: Object,
    priceTagList: Array,
    priceTag: String,
    tagNameList: Array,
    tagName: String,
    currentCookie: String,
    selectedTagClass: Function
  },
  emits: [
    'select-tab',
    'toggle-panel',
    'update-insearch',
    'select-price-tag',
    'selected-tag',
    'toggle-tag',
    'record-price',
    'save-cookie',
    'update-extra',
    'selected-tag-class',
    'update:tagName',
    'update:priceTag',
    'update:filterCondition',
    'update:currentCookie'
  ],
  directives: {
    'click-outside': clickOutside
  },  
  setup() {
    const { scrolled } = mapState()

    return {
      scrolled
    };
  },
  methods: {
    scrollToTop() {
      window.scrollTo({
        top: 0,
        behavior: 'smooth'
      });
    }
  }
}
</script>

<style scoped>
.header {
  position: fixed;
  right: 0;
  top: 4rem;
  z-index: 999;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background: #fbfbfb;
}

.header .header-item {
  padding: 1rem .5rem;
  cursor: pointer;
  width: 2rem;
  word-break: break-all;
}

.header .header-item:not(:first-child) {
  border-top: solid 2px #fff;
}

.header .header-item.curr {
  color: #fff;
  background-color: #06c;
}

.header-item.active {
  background-color: hsl(27, 87%, 72%);
  color: #fff;
}


.search-btn.active .header-box {
  display: flex;
}

.header-box {
  z-index: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}

.header-box-wrap {
  transition-duration: .3s;
  display: flex;
  flex-direction: row;
  position: absolute;
  top: 2rem;
  right: 0;
  transform: translateX(calc(100% - 2.5rem));
  padding: 1rem;
  color: #000;
  box-shadow: 0 2px 10px 0 rgba(0, 0, 0, 0.1);
  border-radius: 6px;
  overflow: hidden;
  background-color: #fff;
  opacity: .95;
}

.header-box-item .search-title {
  width: 2rem;
}

.header-box-item .range-tip {
  width: 2.5rem;
}

.header-box-item {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}

.header-box input[type="text"] {
  border: solid 1px hsl(46, 100%, 91%);
  flex: 1;
  font-size: .8rem;
}

.header-box-left {
  border-right: dashed 2px hsl(46, 100%, 91%);
  margin-right: 1rem;
  align-items: center;
  display: flex;
  flex-direction: column;
  justify-content: space-evenly;
}

.header-box-right {
  display: flex;
  flex-direction: column;
  width: 15rem;
}

.header-box-right .header-box-item {
  padding-bottom: .5rem;
}

.header-box-right .item-main {
  flex: 3;
}

.header-box-right .item-footer {
  display: flex;
  flex-wrap: wrap;
  flex: 1;
}

input[type="range"] {
  -webkit-appearance: none;
  appearance: none;
  background: transparent;
  cursor: pointer;
  width: 8rem;
}

input[type="range"]:focus {
  outline: none;
}

input[type="range"]::-webkit-slider-runnable-track {
  background-color: hsl(27, 87%, 87%);
  border-radius: 0.5rem;
  height: .5rem;
}

input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  margin-top: -2px;
  background-color: hsl(23, 87%, 97%);
  border-radius: 0.5rem;
  height: .75rem;
  width: 1rem;
}

textarea {
  border: solid 1px hsl(46, 100%, 91%);
  overflow: auto;
  outline: none;
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  box-shadow: none;
  resize: none;
  margin-left: 0.25rem;
  margin-top: 0.25rem;
  padding: .25rem;
}

input[type="text"] {
  box-sizing: border-box;
  outline: none;
  border: 0;
  margin: 0 .25rem;
  width: 3rem;
  font-size: 1rem;
  padding: 0 .5rem;
}

.marked {
  background-color: hsl(46, 100%, 91%);
}

.tag-item {
  padding: .25rem;
  border-radius: .5rem;
  margin-right: .25rem;
}

.header .header-box-wrap:hover,
.header.focus .header-box-wrap {
  transform: translateX(0);
}

.cookie-panel {
  width: 20rem;
}

.cookie-panel-content {
  width: 100%;
  display: flex;
  flex-direction: column;
}

.cookie-panel textarea {
  width: 100%;
  min-height: 8rem;
  font-family: monospace;
  font-size: 0.8rem;
  line-height: 1.2;
}

.small {
  font-size: .75rem;
}

.small.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}
</style>
