<template>
  <div>
    <!-- 待发债列表 -->
    <div class="stock-list" v-if="tab == 'new'">
      <div class="stock-row stock-header">
        <div class="stock-item medium">公告日</div>
        <div class="stock-item">代码</div>
        <div class="stock-item ">名字</div>
        <div class="stock-item">评级</div>
        <div class="stock-item">百元含权</div>
        <div class="stock-item large">方案进展</div>
        <div class="stock-item medium">规模</div>
        <div class="stock-item medium">股权登记日</div>
        <div class="stock-item">配售价</div>
        <div class="stock-item">股价</div>
        <div class="stock-item medium" @click="$emit('sort-by', 'diff_rate')">目标价差</div>
        <div class="stock-item">配售10张</div>
        <div class="stock-item">配售率</div>
        <div class="stock-item">安全垫</div>
      </div>

      <!-- 骨架屏 - 待发债 -->
      <div v-if="!stockList || stockList.length === 0">
        <SkeletonItem v-for="i in 5" :key="'skeleton-new-' + i" :rows="2" />
      </div>

      <div class="stock-row" v-for="stock in stockList" :key="stock.stock_id"
        :class="(stock.stock_id.startsWith('00') || stock.stock_id.startsWith('60')) ? '' : 'stock-grey'">
        <div class="stock-item medium">{{ stock.progress_dt }} </div>
        <div class="stock-item selectable">{{ stock.stock_id }}</div>
        <div class="stock-item selectable">{{ stock.stock_nm }}<span class="stock-flg">{{ stock.margin_flg }}</span>
        </div>
        <div class="stock-item ">{{ stock.rating_cd }} </div>
        <div class="stock-item ">{{ stock.cb_amount }} </div>

        <div class="stock-item large" v-html="stock.progress_nm" :title="stock.progress_full"></div>
        <div class="stock-item medium" v-if="stock.amount">{{ stock.amount }}{{ stock.online_amount && ("(" +
          stock.online_amount + ")") || "" }}亿 </div>
        <div v-else class="stock-item medium stock-amount">-</div>

        <div class="stock-item medium" v-if="stock.record_dt">{{ stock.record_dt }}</div>
        <div v-else class="stock-item medium">-</div>
        <div class="stock-item" v-if="stock.ration">{{ stock.ration }}</div>
        <div v-else class="stock-item">-</div>
        <div class="stock-item">{{ stock.price }}</div>
        <div class="stock-item medium"><span v-if="stock.diff_price"
            :title="stock.diff_rate + '%'">{{ stock.diff_price }} /</span><input type="text"
            v-model="markedNewMap[stock.stock_id]"
            @change="$emit('marked-new', stock, markedNewMap[stock.stock_id])" /> </div>

        <div class="stock-item" v-if="stock.apply10">{{ stock.apply10 }}</div>
        <div v-else class="stock-item">-</div>
        <div class="stock-item" v-if="stock.ration_rt">{{ stock.ration_rt }}%</div>
        <div v-else class="stock-item">-</div>
        <div class="stock-item">{{ ((stock.amount >= 10 ? 200: 300) / (Math.floor(Math.ceil(stock.apply10/100) *
          stock.price ))).toFixed(2) }}%</div>
      </div>
    </div>

    <!-- 可转债列表 -->
    <div class="stock-list" v-if="tab == 'now'">
      <div class="stock-row stock-header">
        <div class="stock-item">转债代码</div>
        <div class="stock-item">转债名称</div>
        <div class="stock-item" @click="$emit('sort-by', 'price')">现价</div>
        <div class="stock-item" @click="$emit('sort-by', 'record_diff_rate')" v-if="priceTag">涨跌幅</div>
        <div class="stock-item" @click="$emit('sort-by', 'increase_rt')" v-else>涨跌幅</div>
        <div class="stock-item">正股代码</div>
        <div class="stock-item">正股名称</div>
        <div class="stock-item large">强赎/现值/现价/转股/下修/回售</div>
        <div class="stock-item" @click="$emit('sort-by', 'sincrease_rt')">正股涨跌</div>
        <div class="stock-item" @click="$emit('sort-by', 'pb')">评级/PB</div>
        <div class="stock-item medium">预计进度</div>
        <div class="stock-item" @click="$emit('sort-by', 'premium_rt')">转股溢价率</div>
        <div class="stock-item" @click="$emit('sort-by', 'year_left')">剩余年限</div>
        <div class="stock-item" @click="$emit('sort-by', 'curr_iss_amt')">剩余规模(亿)</div>
        <div class="stock-item medium" @click="$emit('sort-by', 'turnover_rt')">成交额(亿)/换手率</div>
        <div class="stock-item" @click="$emit('sort-by', 'ytm_rt')">到期税前收益率</div>
        <div class="stock-item medium">标签</div>
      </div>

      <!-- 骨架屏 - 可转债 -->
      <div v-if="!stockList || stockList.length === 0">
        <SkeletonItem v-for="i in 8" :key="'skeleton-now-' + i" :rows="3" />
      </div>

      <div class="stock-row" v-for="stock in stockList" :key="stock.bond_id">
        <div class="stock-item" :title="stock.option_tip">{{ stock.bond_id }} </div>
        <div class="stock-item" :title="stock.bond_nm_tip">{{ stock.bond_nm }} </div>
        <div class="stock-item" :title="stock.record_price">{{ stock.price }} </div>
        <div class="stock-item" v-if="priceTag">{{ stock.record_diff_rate }} %</div>
        <div class="stock-item" v-else>{{ stock.increase_rt }} % </div>
        <div class="stock-item">{{ stock.stock_id }} </div>
        <div class="stock-item">{{ stock.stock_nm }}<span class="stock-flg">{{ stock.margin_flg }}</span> </div>
        <div class="stock-item large">
          <span class="small stock-price" :title="forceTip(stock)"
            :tip="stock.forceStatus">{{ stock.forceStockPrice }}</span>
          <span class="small stock-price">{{ (stock.sprice * (1+ stock.premium_rt/100)).toFixed(2) }} </span>
          <span class="small stock-price" :title="stock.convert_cd_tip">{{ stock.sprice }}</span>
          <span class="small stock-price" :title="stock.convert_price_tips"
            :tip="stock.convert_price_tips">{{ stock.convert_price }}</span>
          <span class="small stock-price" :title="downTip(stock)"
            :tip="stock.downStatus">{{ stock.downStockPrice }}</span>
          <span class="small stock-price" :title="resoldTip(stock)" :tip="stock.resoldStatus"
            v-if="stock.resoldRate != '0' ">{{ stock.resoldStockPrice }}</span><span v-else
            class="small stock-price">-</span>
        </div>
        <div class="stock-item">{{ stock.sincrease_rt }}% </div>
        <div class="stock-item">{{ stock.rating_cd }} / {{ stock.pb }}</div>
        <div class="stock-item medium">{{ progressTip(stock) }}</div>
        <div class="stock-item">{{ stock.premium_rt }}% </div>
        <div class="stock-item" :title="stock.maturity_dt">{{ stock.year_left }} </div>
        <div class="stock-item">{{ stock.curr_iss_amt }} </div>
        <div class="stock-item medium">{{ (stock.volume/10000).toFixed(2) }} / {{ stock.turnover_rt }}%</div>
        <div class="stock-item" v-if="stock.ytm_rt">{{ stock.ytm_rt }}%</div>
        <div v-else class="stock-item">-</div>
        <div class="stock-item medium"><span class="small tag-item marked" v-for="tagName in stock.tagNameList" :key="tagName">
            {{ tagName }}</span></div>
      </div>
    </div>
  </div>
</template>

<script>
import SkeletonItem from '../../components/SkeletonItem.vue'

export default {
  name: 'StockList',
  components: {
    SkeletonItem
  },
  props: {
    tab: String,
    stockList: Array,
    priceTag: String,
    markedNewMap: Object,
    progressTip: Function,
    downTip: Function,
    forceTip: Function,
    resoldTip: Function
  },
  emits: ['sort-by', 'marked-new']
}
</script>

<style scoped>
.stock-list {
  display: flex;
  flex-direction: column;
  position: relative;
}

.stock-list .stock-row {
  display: flex;
  align-items: center;
  border-bottom: solid 1px #eee;
  min-height: 3rem;
}

.stock-list .stock-row .stock-item {
  flex: 1;
  min-width: 6rem;
  max-width: 6rem;
  overflow: hidden;
  text-align: center;
}

.stock-list .stock-row .stock-item.large {
  min-width: 14rem;
  max-width: 14rem;
  font-size: .85rem;
}

.stock-list .stock-row .stock-item.medium {
  min-width: 10rem;
  max-width: 10rem;
}

.stock-grey {
  color: grey;
}

.stock-header {
  top: 0;
  z-index: 99;
  background: #fff;
  position: sticky;
  user-select: none;
}

.stock-flg {
  font-size: 12px;
  margin-left: 0.25rem;
  user-select: none;
  vertical-align: top;
}

.selectable {
  user-select: all;
}

.stock-red {
  color: red;
}

.stock-green {
  color: green;
}

.stock-item[title]:not([title=""]) {
  color: #06c;
  cursor: pointer;
}

.stock-price {
  padding: 0 .25rem;
}

.stock-price[tip]:not([tip=""]) {
  color: #06c;
  cursor: pointer;
}

.marked {
  background-color: hsl(46, 100%, 91%);
}

.tag-item {
  padding: .25rem;
  border-radius: .5rem;
  margin-right: .25rem;
}

.stock-row:hover {
  background-color: #f9f9f9;
}

.small {
  font-size: .75rem;
}

input[type="text"] {
  box-sizing: border-box;
  outline: none;
  border: 0;
  margin: 0 .25rem;
  width: 3rem;
  font-size: 1rem;
}
</style>
