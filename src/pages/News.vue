<template>
  <BodyLayout :available="loading || !!totalCount">
    <div class="container mx-auto px-4 py-6">
      <!-- 文章列表 -->
      <div class="grid gap-4">
        <div v-for="article in articles" :key="article.path"
          @click="goToGithubPage(article)"
          class="bg-white rounded-lg transition-all duration-200 overflow-hidden cursor-pointer">
          <div class="p-6">
            <div class="flex items-start">
              <div class="flex-1">
                <h3 class="text-lg font-semibold mb-2 text-gray-900 hover:text-blue-600 transition-colors duration-200">
                  {{ article.title }}
                </h3>
                <p v-if="article.summary" class="text-gray-600 text-sm mb-3 leading-relaxed">
                  {{ article.summary }}
                </p>
                <div class="flex items-center text-sm text-gray-500 space-x-4">
                  <span class="flex items-center">
                    <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"></path>
                    </svg>
                    {{ article.path }}
                  </span>
                  <span class="flex items-center">
                    <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M17.707 9.293a1 1 0 010 1.414l-7 7a1 1 0 01-1.414 0l-7-7A.997.997 0 012 10V5a3 3 0 013-3h5c.256 0 .512.098.707.293l7 7zM5 6a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd"></path>
                    </svg>
                    新闻AI时评
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 骨架屏加载 -->
      <div v-if="loading && articles.length === 0" class="grid gap-4">
        <SkeletonItem v-for="i in 10" :key="i" :rows="3" />
      </div>

      <!-- 加载更多状态 -->
      <div v-if="loading && articles.length > 0" class="text-center py-8">
        <div class="inline-flex items-center px-4 py-2 text-sm text-gray-600">
          <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-gray-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          加载中...
        </div>
      </div>

      <!-- 无数据提示 -->
      <div v-if="!loading && articles.length === 0 && !error" class="text-center py-12">
        <div class="max-w-sm mx-auto">
          <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
          </svg>
          <h3 class="text-lg font-medium text-gray-900 mb-2">暂无文章</h3>
          <p class="text-gray-500">当前仓库中没有找到文章内容</p>
        </div>
      </div>

      <!-- 加载完成提示 -->
      <div v-if="!loading && !hasMore && articles.length > 0" class="text-center py-8">
        <div class="inline-flex items-center px-4 py-2 text-sm text-gray-500 bg-gray-100 rounded-full">
          <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
          </svg>
          已加载全部文章
        </div>
      </div>
    </div>

    <template  v-slot:slider>
       <!-- Add RecommendationList here, bind to previewLinkList from store -->
      <RecommendationList title="精选内容" :recommendations="previewLinkList" />
    </template>
  </BodyLayout>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch, inject } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import SkeletonItem from '../components/SkeletonItem.vue'
import BodyLayout from '../components/BodyLayout.vue'
import RecommendationList from '../components/RecommendationList.vue'
import { mapState} from '../stateutil'


const http = inject('http')
const route = useRoute()
const router = useRouter()
const articles = ref([])
const currentPage = ref(1)
const loading = ref(false)
const hasMore = ref(true)
const error = ref('')
const totalCount = ref(0)
const currentRepo = ref('')

const { previewLinkList } = mapState()

// 获取文章列表
const fetchArticles = async (page = 1) => {
  if (loading.value || !hasMore.value) return
  
  loading.value = true
  error.value = ''
  
  try {
    const repo = 'gitbook'
    currentRepo.value = repo
    
    const response = await http.primary().get(`/github/${repo}/articles?page=${page}&limit=10`)
    const data = response.data
    
    if (page === 1) {
      articles.value = data.articles
    } else {
      articles.value.push(...data.articles)
    }
    
    totalCount.value = data.total
    hasMore.value = data.page < data.totalPages
    currentPage.value = page

  } catch (err) {
    error.value = err.message || '获取文章列表失败'
    hasMore.value = false
  } finally {
    loading.value = false
  }
}

// 检查滚动位置
const checkScroll = () => {
  const scrollHeight = document.documentElement.scrollHeight
  const scrollTop = document.documentElement.scrollTop
  const clientHeight = document.documentElement.clientHeight
  
  // 当滚动到距离底部100px时加载更多
  if (scrollHeight - scrollTop - clientHeight < 100 && !loading.value && hasMore.value) {
    fetchArticles(currentPage.value + 1)
  }
}

// 监听路由变化
watch(() => route.params.repo, (newRepo) => {
  if (newRepo !== currentRepo.value) {
    currentPage.value = 1
    hasMore.value = true
    error.value = ''
    fetchArticles(1)
  }
})

// 监听滚动事件
onMounted(() => {
  fetchArticles()
  window.addEventListener('scroll', checkScroll)
})

onUnmounted(() => {
  window.removeEventListener('scroll', checkScroll)
})

const goToGithubPage = (article) => {
  router.push(`/a/${article.path}`)
}


</script>

<style>

</style>