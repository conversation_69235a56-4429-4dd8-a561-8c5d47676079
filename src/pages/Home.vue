<script setup>
  import BodyLayout from "../components/BodyLayout.vue";
  import Main from "./Main.vue";
  import HomeSlave from "./HomeSlave.vue";

  import { inject, onMounted, ref, computed} from 'vue'
  import { mapState, mapGetters, mapMutations, mapActions } from '../stateutil'
  const http = inject('http')

   const { ready, fetchLinkList, fetchSearchTop } = mapMutations()
   
  onMounted(async () => {

    http.get('/link/lists').then((resp) => {
      fetchLinkList(resp.data);
    })

    http.get('/news/data/baidu').then((resp) => {
      fetchSearchTop(resp.data.data.splice(0, 10));
    })
  })

</script>

<template>
  <BodyLayout :spacing="false">
    <Main></Main>
    <template v-slot:slider>
      <HomeSlave/>
    </template>
  </BodyLayout>
</template>

<style scoped>

</style>
