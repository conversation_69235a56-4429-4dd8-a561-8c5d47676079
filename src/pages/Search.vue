<script setup>
import {inject, onMounted, ref, watch} from 'vue'
import { mapState, mapGetters, mapMutations, mapActions } from '../stateutil'
import { useRoute, useRouter } from 'vue-router'
import SkeletonItem from '../components/SkeletonItem.vue'

const route = useRoute();


defineProps({
  result: Object,
  searching: Boolean
})


const { setting } = mapState()


let searchUrl = (query) => {

  let params = new URLSearchParams(route.query)
  for(const key in query){
    params.set(key, query[key]);
  }
  if(query['start'] === undefined) {
    params.delete('start');
    !query['nfpr'] && params.delete('nfpr');
  }
  if(!setting.safe) {
    query['safe'] = 'inactive'
  }
  return '/search?' + params.toString();
}



onMounted(() => {

})
</script>

<template>
   <div class="search-container">
      <SkeletonItem v-if="searching " :rows="50"></SkeletonItem>
      <template v-else>

        <div class="search-tip-container" v-if="result.total">
         <span class="search-tip" v-if="result.total">{{result.total}}</span>
         <span class="search-tip" v-if="result.hint"> 实际搜索：{{result.hint}} </span>
         <span class="search-tip" v-if="result.orig"> 还原搜索: <a :href="searchUrl({q: result.orig, nfpr: 1})">{{result.orig}}</a> </span>
         <span class="search-tip" v-if="result.advice"> 搜索建议：<a :href="searchUrl({q: result.advice})">{{result.advice}}</a>  </span>
        </div>


        <ul class="search-list">
           <li class="search-item" v-for="item in result.items">
             <div v-if="item.type === 'image'">
               <div v-html="item.html"/>
             </div>
             <div v-else-if="item.type === 'group'">
               <div>
                 <a class="title" target="_blank" :href="item.titleLink" v-html="item.title"></a>
                 <div v-for="link in item.link">
                   <a class="group-title" target="_blank" :href="link.href" v-html="link.title"></a>
                   <span class="group-desc" v-html="link.description"></span>
                 </div>
               </div>
             </div>
             <div v-else-if="item.type === 'video'">
               <div>
                  <div>{{item.title}}</div>

                   <a v-for="link in item.link" class="video-link" target="_blank" :href="link.href" v-html="link.description"></a>

               </div>
             </div>
             <div v-else>
               <div>
                 <a class="title" target="_blank" :href="item.link"> {{item.title}}</a>
                 <div>
                   <span v-if="item.description" class="desc" v-html="item.description"></span>
                   <template v-for="link in item.linkGroupList">
                     <div v-if="!link.href.startsWith('/search')">
                       <a class="group-title" target="_blank" :href="link.href" v-html="link.title"></a>
                       <span class="group-desc" v-html="link.description"></span>
                     </div>
                     <span class="group-desc" v-else>
                       <a class="group-title" target="_blank" :href="link.href" v-html="link.title"></a>
                       <span class="group-desc" v-html="link.description"></span>
                     </span>

                   </template>


                 </div>
               </div>
             </div>
           </li>
        </ul>
        <div class="not-result-container" v-if="result.notResult" v-html="result.notResult"></div>


        <div class="page link-group" v-if="result.pagin">
         <template v-for="pageItem in result.pagin">

           <a v-if="pageItem.active" class="" :href="searchUrl({q: result.q, start: pageItem.page})">
             <span class="active"> {{pageItem.text}}</span>
           </a>
           <span class="curr" v-else>
             {{pageItem.text}}
           </span>
         </template>
        </div>
        <div class="search-footer">
          已显示全部内容
        </div>
      </template>


 </div>

</template>

<style scoped>

.search-tip-container, .not-result-container {
  margin: 1rem;
}

.search-tip-container {
  margin-bottom: .5rem;
}

.search-tip {
  margin-right: 1rem;
  color: #999;
}


address, cite, dfn, em, var, b {
  font-style: normal;
}

b{
  font-weight: normal;
}

ul, ol {
  list-style: none;
}

a {
  color: #06c;
  text-decoration: none;
}

a:hover{
  text-decoration: underline;
}



.link-group span{
  border: 1px solid #f1f3ff;
  display: inline-block;
  box-sizing: border-box;
  min-width: 34px;
  padding: 0 10px;
  line-height: 34px;
  height: 34px;
}

.link-group span.active {
  /*	border: 1px solid #e1e2e3;*/
  cursor: pointer;
}

.link-group span.curr{
  text-align: center;
  /*border: 1px solid #e1e2e3;*/
  margin-right: 9px;
}

.link-group a{
  display: inline-block;
  text-align: center;
  line-height: 36px;
  text-decoration: none;
  background: white;
  margin-right: 9px;
}

.link-group a:hover span.active{
  background-color: rgba(242, 248, 255, .5);
}

.link-group{
  font: 14px arial;
  white-space: nowrap;
  padding: 1rem ;
}


.search-item {
  line-height: 32px;
  padding: 10px 1.2rem;
  overflow-wrap: break-word;
  overflow: hidden;
}

.search-item:not(:last-child){
  border-bottom: 1px solid #f3f3f3;
}


.search-item a{
  color: #06c;
  text-decoration: none;
}

.search-item span.desc > a {
  margin-right: 1rem;
}

.search-item:hover .title, .search-item .soft-link:hover, .search-item .group-title:hover{
  text-decoration: underline;
}

.search-item .group-desc {
  margin-right: 10px;
}

.search-item .video-link{
  display: block;
  border-top: 1px solid #f3f3f3;
  color: #333;
}

.search-item .video-link > div[role="text"] span[tabindex="-1"] {
  display: none;
}

.search-item .video-link > div[role="text"] div[role="heading"] {
  font-weight: bold;
  color: #06c;
}

.search-item .video-link:hover > div[role="text"] div[role="heading"] {
  text-decoration: underline;
}


.search-item .video-link > div:first-child:not([role="text"]) > div:nth-child(2) > div:first-child   {
  font-weight: bold;
  color: #06c;
}

.search-item .video-link:hover > div:first-child:not([role="text"])  > div:nth-child(2) > div:first-child   {
  text-decoration: underline;
}


.search-item .video-link > div {
  display: flex;
  align-items: center;
}

.search-item .video-link > div > div {
  margin: .5rem 1rem 0 0;
}

.search-item:hover a{
  color: #06c;
}

.search-item:hover em, .search-item:hover b{
  color: #f00;
}

.search-item a.title, .search-item a.title em,  .search-item a.title b {
  font-weight: bold;
}

.search-item:hover{
  color: #06c;
  padding-top: 11px;
  margin-top: -1px;
  box-shadow: 0 0 8px rgba(0, 0, 0, 0.08);
}

.search-item em, .search-item b{
  color: #c00;
}



.search-footer {
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: .75rem;
  color: #999;
  margin: 1.5rem 0;
  position: relative;
  text-align: center;
}


.search-footer::before,
.search-footer::after {
  content: "";
  flex: 1;
  border-bottom: 1px solid #eee;
}

.search-footer::before {
  margin-right: .5rem;
}

.search-footer::after {
  margin-left: .5rem;
}




</style>
