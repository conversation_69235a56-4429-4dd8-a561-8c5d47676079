<script setup>
  import SearchSlave from "./SearchSlave.vue";
  import Search from "./Search.vue";
  import {useRoute} from "vue-router";
  import {inject, onMounted, ref} from "vue";
  import BodyLayout from "../components/BodyLayout.vue";
  import SkeletonItem from "../components/SkeletonItem.vue";

  const result = ref({})
  const linkList = ref([])

  
  const route = useRoute()

  const http = inject('http')

  const loading = ref(true)
  const ready = ref(false)

  onMounted(() =>  {
    document.title = route.query.q + ' - soga导航'

    http.get('/news/search/complete', {
      params: {
        q: route.query.q
      }
    }).then(async resp => {
      if(resp.data?.length) {
        linkList.value = resp.data.map(item => {
          return {
            title: item,
            url: '/search?q=' + encodeURIComponent(item)
          }
        })
      }
    })


    http.primary().get("/news/search", {
      params: {
        q: route.query.q
      }
    }).then(resp => {
      if(resp.data.items?.length) {
        result.value = resp.data
        ready.value = true
      }
    }).finally(() => {
      loading.value = false
    })
  })
</script>

<template>
  <BodyLayout>
    <Search v-if="ready" :result="result"></Search>
    <SkeletonItem  v-if="loading" :rows="50"/>
    <template v-slot:slider>
      <SearchSlave :linkList="linkList"/>
    </template>
  </BodyLayout>
</template>

<style scoped>

</style>
