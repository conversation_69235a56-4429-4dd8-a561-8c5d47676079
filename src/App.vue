<script setup>
// This starter template is using Vue 3 <script setup> SFCs
// Check out https://v3.vuejs.org/api/sfc-script-setup.html#sfc-script-setup
import Index from './pages/Index.vue'

</script>

<template>
  <div class="flex flex-col text-gray-800 bg-white">
    <Index />
  </div>
</template>

<style>
@import "./assets/css/icon.css";


/* 自定义字体和基础样式 */
body {
  font-family: "Microsoft YaHei", sans-serif;
  font-size: 16px;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.shadow {
  box-shadow: 0 0 8px 4px rgba(0, 77, 153, 0.1);
}

/* 自定义滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-thumb {
  border-width: 0;
  border-style: solid;
  border-radius: 8px;
  background: #d9d9d9;
}

::-webkit-scrollbar-track,
::-webkit-scrollbar-track-piece {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-button {
  display: none;
}

/* 响应式滚动条 */
@media screen and (max-device-width: 1280px) {
  ::-webkit-scrollbar {
    width: 4px;
    height: 4px;
  }

  ::-webkit-scrollbar-thumb {
    border-radius: 4px;
  }

  ::-webkit-scrollbar-track,
  ::-webkit-scrollbar-track-piece {
    width: 4px;
    height: 4px;
  }
}

</style>
