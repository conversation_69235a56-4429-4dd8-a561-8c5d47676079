import { createApp } from 'vue'
import { createHead } from '@vueuse/head'
import App from './App.vue'
import store from './store'
import router from './router'
import axios from "axios";
import './assets/css/tailwind.css'
import { createDialog } from './utils/useDialog.js'

const head = createHead()

import http from './utils/http.js'; 

// 创建应用实例
const app = createApp(App)

app.use(store)
  .use(router)
  .use(head)
  .use(http)

createDialog(app)

app.mount('#app')
