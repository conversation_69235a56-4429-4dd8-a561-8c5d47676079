import { createApp } from 'vue'
import { createHead } from '@vueuse/head'
import App from './App.vue'
import store from './store'
import router from './router'
import axios from "axios";
import './assets/css/tailwind.css'
import { createDialog } from './utils/useDialog.js'

const head = createHead()

// 设置基础URL
// 在开发环境中，我们使用Vite的代理功能，所以baseURL可以是相对路径
// 在生产环境中，可以设置为空字符串（使用相对路径）或者设置为API的绝对URL
axios.defaults.baseURL = process.env.NODE_ENV === 'production' ? 'https://api.889990.xyz' : 'http://localhost:3000';

// 配置跨域请求携带凭证
axios.defaults.withCredentials = true;

//30s
axios.defaults.timeout = 30000


axios.interceptors.response.use(
  response => {
    return response.data;
  },
  error => {
    return Promise.reject(error);
  }
);

// 创建应用实例
const app = createApp(App)

app.use(store)
  .use(router)
  .use(head)
  .provide("http", axios)

createDialog(app)

app.mount('#app')
