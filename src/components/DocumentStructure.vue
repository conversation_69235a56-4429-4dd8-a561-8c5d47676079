<script setup>
// import { useRoute } from 'vue-router'; // 不再需要 useRoute
import AppLink from "./AppLink.vue";
import { defineProps } from "vue"; // 移除未使用的 computed

const props = defineProps({
  headings: {
    type: Array,
    default: () => []
  },
  activeHeadingId: { // 添加 activeHeadingId prop
    type: String,
    default: null
  },
  // 将 title prop 移到这里
  title: {
    type: String,
    default: '📑 文档目录'
  }
}); // 确保只有一个正确的 defineProps 调用

// Method to generate the class object for cleaner template
const getClassObject = (heading) => {
  const classes = {
    'structure-item': true,
    'active': props.activeHeadingId === heading.id,
  };
  if (heading.level !== undefined) {
    classes['indent-level-' + heading.level] = true;
  }
  return classes;
};

// 如果需要基于 level 调整样式，可以保留，但确保 headings 包含 level
// 如果 headings 数组直接可用，则无需 processedHeadings

</script>

<template>
  <div class="document-structure"> <!-- Temporarily remove v-if -->
    <div class="structure-title">{{ props.title }}</div> <!-- Use props.title -->
    <div class="structure-content">
       <!-- Restore v-for loop and dynamic content -->
       <div
         v-for="heading in props.headings"
         :key="heading.id"
         :class="getClassObject(heading)"
       >
         <AppLink
           :to="heading.url"
           :class="{ 'active': props.activeHeadingId === heading.id }"
           :activeClass="'active'"
         >
           {{ heading.title }}
         </AppLink>
       </div>
    </div>
  </div>
</template>

<style scoped>
.document-structure {
  background-color: transparent;
  margin-bottom: 1.5rem;
}

.structure-title {
  padding: 0.75rem 1rem;
  font-weight: bold;
  color: #333;
  font-size: 1rem;
}

.structure-content {
  padding: 0 0 0.5rem 0;
}

.structure-item {
  padding: 0.25rem 1rem;
  transition: background-color 0.2s;
}

.structure-item a {
  color: #0366d6;
  text-decoration: none;
  font-size: 0.9rem;
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.structure-item:hover {
  background-color: rgba(0, 0, 0, 0.03);
}

.structure-item.active {
  background-color: rgba(0, 0, 0, 0.05);
}

.structure-item a.active {
  font-weight: bold;
}

/* 缩进级别 */
.indent-level-1 {
  padding-left: 1rem;
}

.indent-level-2 {
  padding-left: 1.25rem;
}

.indent-level-3 {
  padding-left: 1.5rem;
}

.indent-level-4 {
  padding-left: 1.75rem;
}

.indent-level-5 {
  padding-left: 2rem;
}

.indent-level-6 {
  padding-left: 2.25rem;
}

.structure-content::-webkit-scrollbar {
  width: 4px;
  height: 8px;
}

.structure-content::-webkit-scrollbar-thumb {
  border-radius: 4px;
  background-color: transparent;
}

.structure-content::-webkit-scrollbar-track {
  border-radius: 4px;
  background-color: transparent;
}

.structure-content:hover::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2);
}
</style>
