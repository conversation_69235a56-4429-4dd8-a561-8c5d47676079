<script setup>
import { ref,defineEmits, computed } from 'vue'
import AppLink from "./AppLink.vue";
import {mapState} from "../stateutil";

const props = defineProps({
  title: String,
  update: Number,
  sourceKey: String,
  iconColor: String,
  linkList: Array,
  hide: <PERSON><PERSON><PERSON>,
  editable: <PERSON><PERSON><PERSON>,
  dropable: <PERSON><PERSON><PERSON>,
  overhidden: <PERSON><PERSON><PERSON>
})

const emit = defineEmits(['subscribe'])

function timeDesc(time){
  if(time) {
    return '(' + timeFormat(time) + ')'
  }

  return ''
}

function timeFormat(time) {
  let second = (Date.now() - new Date(time).getTime()) / 1e3
      , text = ""
      , min = second / 60;
  if (min < 1)
    text = "刚刚更新";
  else {
    let hour = min / 60;
    if (hour < 1)
      text = Math.floor(min) + "分钟前";
    else {
      let day = hour / 24;
      text = day < 1 ? Math.floor(hour) + "小时前" : Math.floor(day) + "天前"
    }
  }
  return text
}


const subscribe = () => {
  emit('subscribe')
}

</script>

<template>
  <div class="main-container" :class="hide ? 'unselected' : ''" >
    <div class="header">
      <span v-if="sourceKey" class="iconfont " :class="'icon-' + sourceKey" :style="{color: iconColor}"></span>
      <span class="title" >{{title}}</span>
      <span class="subtitle" v-text="timeDesc(update)"></span>
      <span class="extra" >
        <span v-if="editable && (!hide ? dropable : true)" class="icon iconfont " :class="hide ? 'icon-yanjing_xianshi' : 'icon-yanjing_yincang'"  v-on:click="subscribe()"></span>
      </span>
    </div>

  
    <div class="content" :class="overhidden ? 'overhidden': ''">
        <AppLink class="link" v-for="(link, index) in linkList" :to="link.link" :title="link.title"  >
          <span class="link-index" >{{index + 1}}.</span>
          <span class="link-title">{{link.title}}</span>
          <span class="link-extra">{{link.extra}}</span>
        </AppLink>
    </div>


  </div>

</template>

<style scoped>

.content::-webkit-scrollbar {
  width: 5px;
  height: 8px;
}

.content::-webkit-scrollbar-thumb {
  border-radius: 0;
  background-color: transparent;
}

.content::-webkit-scrollbar-track {
  border-radius: 0;
  background-color: transparent;
}

.content:hover::-webkit-scrollbar-thumb {
  border-radius: 0;
  background-color: #efefef;
}

.main-container {
  display: flex;
  flex-direction: column;
  background-color: #fff;
  padding: .5rem;
 /* box-shadow: 0 0 8px 4px rgba(0, 77, 153, 0.1); */
}

.header {
  display: flex;
  justify-content: left;
  align-items: center;
  padding: .5rem;
  flex: 1;
 /* background: linear-gradient(90deg, #f7f7f7 60%, hsla(0, 0%, 97%, 0));*/
  background: linear-gradient(90deg,hsla(48, 97%, 85%, .3), hsla(0, 0%, 97%, 0));
}


.header .extra {
  justify-self: flex-end;
  flex: 1;
  display: none;
  justify-content: flex-end;
  padding-right: 0.75rem;
  font-size: 14px;
  color: #ddd;
}

.header:hover .extra {
  display: flex;
}

.title {
  font-size: 14px;
  user-select: none;
  font-weight: bold;
  margin-right: .5rem;
}

.subtitle {
  user-select: none;
  font-weight: normal;
  font-size: 12px;
}

.content {
  display: flex;
  flex-direction: column;
  height: 20rem;
  overflow-y: auto;
  padding-right: .5rem;
  padding-left: .5rem;
}

.link:hover .link-title {
  color: #3071f2;
}

.link {
  display: flex;
  text-decoration: none;
  justify-content: space-between;
  align-items: center;
  line-height: 2rem;
  color: #333;
  font-size: .75rem;
}

.content .link-index {
  font-weight: bold;
  margin-right: 4px;
  color: #999;
}
.content .link:first-child .link-index {
  color: #cc3939;
}

.content .link:nth-child(2) .link-index {
  color: #de6b30;
}

.content .link:nth-child(3) .link-index  {
  color: #cc984f;
}

.link .link-index, .link .link-extra {
  flex-grow: 0;
  flex-shrink: 0;
}

.link-extra {
  font-size: .75rem;
  color: #666;
}

.link .link-title {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  flex: 1;
  margin: 0 .25rem;
}

.iconfont{
  margin-right: .5rem;
  height: 1.5rem;
}

.icon-aifaner {
  color: #000 !important;
}

.unselected {
  filter: opacity(0.5);
}

.content.overhidden {
  overflow-y: hidden;
}

.extra .icon:hover {
  cursor: pointer;
}


@media screen and (max-device-width: 1280px){
  .content {
    overflow-y: hidden;
  }
}
</style>
