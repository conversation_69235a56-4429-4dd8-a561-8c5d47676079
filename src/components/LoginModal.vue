<script setup>
import { ref, computed, onMounted, inject, onUnmounted } from 'vue'
import { useStore } from 'vuex'

const http = inject('http')
const store = useStore()

// 获取用户状态
const user = computed(() => store.state.user)

// 登录相关状态
const userMenuOpen = ref(false)

// 获取当前页面URL作为登录后的重定向地址
const currentUrl = computed(() => {
  return window.location.href
})

// 获取用户信息
const fetchUserInfo = async() => {
  http.get('/auth/me').then(response => {
    store.commit('setUser', response.data)
  }).catch(error => {
    store.commit('setUser', null)
  })
}

// 登出功能
const logout = async() => {
  http.get('/auth/logout').finally(response => {
    store.commit('setUser', null)
  })
}

// GitHub登录
const loginWithGithub = () => {
  window.location.href = `${http.defaults.baseURL}/auth/github/login?state=${encodeURIComponent(currentUrl.value)}`
}

// LinuxDO登录
const loginWithLinuxDO = () => {
  window.location.href = `${http.defaults.baseURL}/auth/linuxdo/login?state=${encodeURIComponent(currentUrl.value)}`
}

// 切换用户菜单显示状态
const toggleUserMenu = () => {
  userMenuOpen.value = !userMenuOpen.value
}

// 处理登出
const handleLogout = async () => {
  await logout()
  userMenuOpen.value = false
}

// 点击其他地方时关闭用户菜单
const closeUserMenu = (e) => {
  if (!e.target.closest('.user-area')) {
    userMenuOpen.value = false
  }
}

// 组件挂载时检查用户登录状态并添加事件监听器
onMounted(() => {
  fetchUserInfo()
  document.addEventListener('click', closeUserMenu)
})

// 组件卸载前移除事件监听器
onUnmounted(() => {
  document.removeEventListener('click', closeUserMenu)
})

</script>

<template>
  <div class="user-area">
    <!-- 未登录状态 -->
    <div v-if="!user" class="hidden md:flex login-options lg:gap-4 gap-0">
      <button class="login-button p-2" @click="loginWithGithub">
        <i class="iconfont icon-github-line"></i>
        <span class="hidden lg:block">GitHub</span>
      </button>
      <button class="login-button p-2" @click="loginWithLinuxDO">
        <i class="icon-linuxdo"></i>
        <span class="hidden lg:block">LinuxDo</span>
      </button>
    </div>
    <div v-if="!user" class="flex md:hidden relative">
      <div class="p-2" @click="toggleUserMenu">
        <svg t="1749613725803" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4475" width="20" height="20"><path d="M839.361281 780.794909c-49.341778-47.387263-118.219495-67.911716-159.004387-71.819722-8.061606-0.964978-15.876596-3.906983-20.750603-8.779967-3.434216-3.908006-5.882987-8.31027-6.601348-12.712533 31.508621-26.365484 45.903469-62.028729 57.874105-92.797499 6.109138-15.136745 11.475356-27.845185 15.158235-31.259958l0-0.494257 0.471744-0.493234 0.49528-0.471744c14.661931-12.710486 34.673707-38.110993 45.430702-66.429968 10.733459-28.835745 13.18223-61.555961-5.390777-90.865497 5.885034-20.502963 10.261715-41.523719 12.71151-60.567447 3.412726-24.90625 3.412726-48.351218-0.49528-70.832232-4.624321-30.272467-26.365484-81.566714-56.659441-116.735701-24.659633-29.30749-56.167231-49.340755-92.324732-46.39875-17.561981-24.433482-46.397726-36.156478-76.918857-41.524742-26.613124-5.368264-53.968146-4.874007-74.000388-4.40124-53.741995 0.988514-101.598956 13.204743-140.181693 39.570227-36.156478 23.940249-64.49899 59.603493-81.09497 108.448968l-1.460258 2.919493c-11.722996 34.675754-17.584494 72.29249-17.11275 109.909226 0 29.30749 4.402263 57.626465 12.95915 83.521229-16.125259 28.340465-13.430894 59.108213-2.695389 87.428212 10.510378 28.340465 30.79026 53.247738 45.185108 66.429968l0.24764 0 0.246617 0.493234 0.741897 0c2.919493 3.90903 8.286734 16.617469 14.643512 31.754215 12.216229 30.767747 27.352975 67.396993 59.603493 94.28027-0.988514 3.908006-2.942005 7.814989-6.109138 11.229762-5.142114 4.872984-12.464893 7.814989-20.773116 8.779967-41.031509 3.906983-109.88569 24.432459-159.226444 71.819722-35.415604 34.180474-61.062727 81.074503-61.062727 143.597489 0 19.043728 15.629979 34.673707 34.180474 34.673707 1.497097 0 2.972705-0.109494 4.425799-0.300852 1.333368 0.196475 2.697435 0.300852 4.086062 0.300852l692.7609 0c1.206478 0 2.390443-0.084934 3.554966-0.234337 1.294482 0.149403 2.609431 0.234337 3.944845 0.234337 18.550495 0 34.17945-15.628956 34.17945-34.180474C900.400472 861.868389 874.527199 814.975383 839.361281 780.794909zM232.186641 830.607408c36.157501-35.168987 86.709851-50.798966 116.736725-52.752458 24.927739-2.448772 47.880497-12.69002 64.004733-29.309536 21.963221-21.98471 26.859741-47.385217 26.859741-76.692706 0-13.675464-7.321756-24.905226-18.078751-30.273491-23.917736-15.135722-36.628222-46.397726-47.363727-72.7847-9.051143-22.457478-17.113773-42.491767-32.002878-56.169277l-0.493234 0-0.966001-1.460258c-8.555863-8.308223-20.774139-23.444968-27.128871-39.569204-10.734482-29.308513 7.343245-30.29498 11.003612-52.753481 1.706875-7.814989 0.246617-16.126282-3.660366-22.481014-8.555863-20.998243-12.710486-46.39875-13.18223-72.29249-0.494257-29.30749 4.626367-59.58098 13.654998-86.440721l0.986467-2.940982c11.969613-33.709753 30.543643-58.121745 54.460356-73.751724 27.601638-19.043728 63.017242-27.352975 103.800088-27.847232 17.09126-0.494257 40.042995-0.966001 60.322877 2.447748 15.384385 2.919493 28.589129 7.814989 33.709753 15.631002l2.201132 2.447748c6.332219 8.779967 14.642488 15.629979 23.940249 20.502963 10.734482 4.895497 22.457478 6.354731 34.428114 4.895497 10.758018-1.953492 23.447015 7.814989 35.168987 21.964244 22.210861 25.89374 37.84084 61.556984 40.783869 82.060971 2.942005 16.618493 2.447748 33.708729 0.246617 52.281737-2.200108 19.537985-7.096628 39.548738-13.924127 61.534472-4.154623 12.216229-0.98749 30.29498 8.06263 40.536228l-0.24764 0c8.31027 8.80248 7.322779 21.4925 2.448772 34.20401-6.354731 17.090237-19.066241 32.226982-27.601638 40.042995l-0.245593 0.493234 0-0.493234c-15.629979 14.169721-23.444968 34.201963-32.968879 57.155744-10.016121 26.386974-22.705118 57.155744-46.89096 71.796186-34.428114 21.4925-8.06263 89.878007 10.486842 107.954711 16.619516 16.618493 39.078017 26.860764 63.511499 29.309536 30.273491 1.953492 81.074503 17.58347 117.477598 52.752458 17.394159 16.8559 31.355125 38.093597 37.250392 64.961524L195.15626 895.567909C200.951243 868.592534 214.65229 847.418282 232.186641 830.607408z" fill="#666" p-id="4476"></path></svg>
      </div>
      <div v-if="userMenuOpen" class="user-dropdown" >
        <button class="dropdown-item login-button" @click="loginWithGithub">
          <i class="iconfont icon-github-line"></i>
          <span class="">GitHub</span>
        </button>
        
        <button class="dropdown-item login-button" @click="loginWithLinuxDO">
          <i class="icon-linuxdo"></i>
          <span class="">LinuxDo</span>
        </button>
      </div>
    </div>

    <!-- 已登录状态 -->
    <div v-else class="user-profile" @click="toggleUserMenu">
      <img v-if="user.avatarUrl" :src="user.avatarUrl" class="user-avatar w-[1rem] h-[1rem] m-2" alt="用户头像">
      <div v-else class="user-avatar-placeholder m-2" >
        {{ user.username ? user.username.charAt(0).toUpperCase() : 'U' }}
      </div>
      <span class="username hidden md:block mr-2">{{ user.username }}</span>

      <!-- 用户下拉菜单 -->
      <div v-if="userMenuOpen" class="user-dropdown">
        <div class="dropdown-item" @click="handleLogout">退出登录</div>
      </div>
    </div>
  </div>
</template>

<style scoped>

.login-options {
  flex-direction: row;
  justify-content: flex-end;
}

.login-button {
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
  gap: 6px;
  color: #333;
  position: relative;
}

.login-button:hover {
  background-color: #f5f5f5;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.iconfont, .icon-linuxdo {
  font-size: 16px;
  color: #666;
}

.icon-linuxdo {
  background-size: 1rem;
  background-repeat: no-repeat;
  background-position: center;
  width: 1rem;
  height: 1rem;
}

.user-profile {
  display: flex;
  align-items: center;
  cursor: pointer;
  position: relative;
  justify-content: flex-end;
}

.user-avatar {
  border-radius: 50%;
  object-fit: cover;
}

.user-avatar-placeholder {
  border-radius: 50%;
  background-color: #06c;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
}

.username {
  font-size: 14px;
  color: #333;
  max-width: 120px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.user-dropdown {
  position: absolute;
  top: calc(100% + 1rem);
  right: 0;
  display: flex;
  flex-direction: column;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  z-index: 100;
  min-width: 100px;
  background-color: #fff;
  margin: 0;
  
}

.dropdown-item {
  font-size: 14px;
  color: #333;
  transition: background-color 0.2s;
  flex: 1;
  padding: 8px;
  text-align: center;
}

.dropdown-item:hover {
  background-color: #f5f5f5;
}

</style>
