<script setup>
import { defineProps } from 'vue';
import AppLink from './AppLink.vue'; // Assuming AppLink is used for navigation

// Define props to accept recommendation data
const props = defineProps({
  title: {
    type: String,
    default: '为你推荐'
  },
  recommendations: {
    type: Array,
    default: () => [] // Default to an empty array
  }
});


</script>

<template>
  <div class="mt-8 p-4 bg-white flex flex-col">
    <h2 class="text-lg font-semibold mb-4 text-gray-700">{{ title }}</h2>
    <ul class="space-y-4">
      <li v-for="item in recommendations" :key="item.id || item.url" class="border-b border-gray-200 pb-4 last:border-b-0">
        <AppLink :to="item.link" class="block group">
          <h3 class="text-base font-medium text-gray-800 group-hover:text-blue-600 mb-1 truncate">{{ item.title }}</h3>
        </AppLink>
       
        <div class="flex items-center justify-between space-x-2 mt-1">
          <span class="text-xs text-gray-500 line-clamp-1 text-ellipsis " >{{ item.preview }}</span>
          <div class="flex items-center space-x-2 ">
            <span v-if="item.source" class="text-xs bg-gray-100 text-gray-600 px-2 py-0.5 rounded truncate">
                {{ item.source }}
            </span>
            <span v-if="item.extra" class="text-xs bg-gray-100 text-gray-600 px-2 py-0.5 rounded truncate">
                {{ item.extra }}
            </span>
          </div>
        </div>
      </li>
    </ul>
     <p v-if="!recommendations.length" class="text-center text-gray-500 py-4">暂无推荐内容</p>
  </div>
</template>

<style scoped>
/* Add any component-specific styles here if needed */
</style>