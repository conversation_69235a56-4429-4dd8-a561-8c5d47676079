<script setup lang="ts">
import { defineProps } from 'vue';

defineProps({
  spacing: Boolean
})

</script>

<template>
  <div class="flex flex-auto min-w-0"> <!-- overflow-hidden min-w-0 -->
    <template v-if="spacing">
      <div class="hidden md:flex flex-1/6 "></div>
      <div class="flex-5/6 min-w-0">
        <slot />
      </div>
    </template>
    <slot v-else/>
  </div>
</template>

<style scoped>

</style>
