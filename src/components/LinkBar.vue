<script setup>
import {inject, ref, computed} from 'vue'
import AppLink from "./AppLink.vue";
import LinkDialog from "./LinkDialog.vue";

defineProps({
  linkList: Array
})


let submitDialog = ref(false)

const openDialog = () => {
  submitDialog.value = true
}

const closeDialog = () => {
  submitDialog.value = false
}

const handleSubmit = (data) => {
  // 可以在这里处理提交成功后的逻辑
}
</script>

<template>

  <div class="main-container">
    <div class="header ">
   
      <svg t="1749524956316" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="26537" width="20" height="20"><path d="M613.044 62.633H406.865c-30.877 0-55.987 25.123-55.987 55.987V324.8c0 30.864 25.11 55.987 55.987 55.987h206.18c30.876 0 55.986-25.123 55.986-55.987V118.62c0-30.864-25.108-55.987-55.987-55.987zM406.865 324.8V118.62h206.18l0.027 206.179H406.865z m206.18 313.833h-206.18c-30.877 0-55.987 25.123-55.987 55.987v206.179c0 30.864 25.11 55.987 55.987 55.987h206.18c30.876 0 55.986-25.123 55.986-55.987V694.619c0-30.864-25.108-55.987-55.987-55.987z m-206.18 262.166V694.619h206.18l0.027 206.179H406.865zM259.07 588.528c-15.473 0-27.993 12.52-27.993 27.992v86.14C141.78 660.444 97.56 556.04 131.979 461.398c22.034-60.553 72.334-107.738 134.582-126.217 14.817-4.428 23.264-20.01 18.863-34.828-4.428-14.817-19.847-23.182-34.827-18.863-79.141 23.537-143.138 83.624-171.214 160.771C35.86 561.837 89.81 693.555 200.413 749.955H92.037c-15.473 0-27.993 12.52-27.993 27.993s12.521 27.994 27.993 27.994h195.025v-189.42c0-15.474-12.52-27.995-27.993-27.995zM821.463 336.34H924.51c15.473 0 27.994-12.52 27.994-27.993s-12.521-27.994-27.994-27.994H729.486v189.421c0 15.473 12.52 27.993 27.994 27.993s27.993-12.52 27.993-27.993v-88.938c89.296 42.216 133.515 146.62 99.097 241.262-22.034 60.553-72.334 107.738-134.582 126.217-14.817 4.428-23.264 20.01-18.863 34.828 4.428 14.817 19.847 23.182 34.827 18.863 79.142-23.537 143.138-83.624 171.214-160.771 42.82-117.64-8.708-247.03-115.703-304.895z" fill="#515151" p-id="26538"></path></svg>

      <span class="ml-2 title" >友情链接</span>
  
      <span class="extra" v-on:click="openDialog">提交链接</span>
    </div>
    
    <div class="content">
      <AppLink class="link" v-for="(link, index) in linkList" :to="link.link" :title="link.title" >
        <span class="link-title" :class="link.hot ? 'link-hot' : ''">{{link.title}}</span>
      </AppLink>
    </div>

    <LinkDialog
      :visible="submitDialog"
      @close="closeDialog"
      @submit="handleSubmit"
    />
  </div>



</template>

<style scoped>

.content::-webkit-scrollbar {
  width: 5px;
  height: 8px;
}

.content::-webkit-scrollbar-thumb {
  border-radius: 0;
  background-color: transparent;
}

.content::-webkit-scrollbar-track {
  border-radius: 0;
  background-color: transparent;
}

.content:hover::-webkit-scrollbar-thumb {
  border-radius: 0;
  background-color: #efefef;
}

.main-container {
  display: flex;
  flex-direction: column;
  background-color: #fff;
  padding: .5rem;
}

.header {
  display: flex;
  user-select: none;
  justify-content: left;
  align-items: center;
  padding: .5rem;
  background: linear-gradient(90deg,hsla(48, 97%, 85%, .3), hsla(0, 0%, 97%, 0));
}


.title {
  font-size: 14px;
  font-weight: bold;
  margin-right: .5rem;
}

.extra {
  cursor: pointer;
  font-size: 12px;
}

.header .extra:hover {
  color: #3071f2;
}

.content {
  display: flex;
  flex-wrap: wrap;
  align-items: flex-start;
  justify-content: flex-start;
  max-height: 8rem;
  overflow-y: scroll;
}

.link:hover {
  color: #3071f2;
}

.link {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  max-width: 8rem;
  overflow: hidden;
  text-decoration: none;
  line-height: 2rem;
  margin: 0 .25rem;
  color: #333;
  font-size: .75rem;

}

.link .link-title {
  flex: 1;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  cursor: pointer;
}

.link .link-title.link-hot {
  color: #c00;
}





</style>
