<script setup>
import {computed} from 'vue'
import { useStore } from 'vuex'

const store = useStore()

const loading = computed(() => store.state.loading)

</script>

<template>
  <div class="container-wrap" :class="loading ? '': 'hidden'">
      <div class="loader">
        soga导航
      </div>
  </div>
</template>

<style scoped>

.hidden {
  display: none;
}

.container-wrap {
  background-color: #fff;
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 999;
}


.loader {
  width: 250px;
  height: 50px;
  line-height: 50px;
  text-align: center;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-family: helvetica, arial, sans-serif;
  text-transform: uppercase;
  font-weight: 900;
  color: #06c;
  letter-spacing: 0.2em
}

.loader::before,
.loader::after {
  content: "";
  display: block;
  width: 15px;
  height: 15px;
  background: #06c;
  position: absolute;
  animation: load .7s infinite alternate ease-in-out
}

.loader::before {
  top: 0
}

.loader::after {
  bottom: 0
}

@keyframes load {
  0% {
    left: 0;
    height: 30px;
    width: 15px
  }

  50% {
    height: 8px;
    width: 40px
  }

  100% {
    left: 235px;
    height: 30px;
    width: 15px
  }
}
</style>
