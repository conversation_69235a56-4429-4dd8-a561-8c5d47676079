<script setup>

defineProps({
  header: String,
  linkList: Array,
})

</script>

<template>

  <div class="main-container" >
    <div class="main-header">{{ header }}</div>
    <div class="main-body">
      <a class="hash-link" v-for="link in linkList" :class="link.link === $route.hash ? (link.tag + ' active'): link.tag" :href="link.link" :data-id="link.id">
        <span v-if="link.type">{{ link.type === 'dir' ? '📂' : '📄'}} &nbsp;</span> {{ link.title }}
      </a>
    </div>
  </div>

</template>

<style scoped>

.main-body::-webkit-scrollbar {
  width: 5px;
  height: 8px;
}

.main-body::-webkit-scrollbar-thumb {
  border-radius: 0;
  background-color: transparent;
}

.main-body::-webkit-scrollbar-track {
  border-radius: 0;
  background-color: transparent;
}

.main-body:hover::-webkit-scrollbar-thumb {
  border-radius: 0;
  background-color: #efefef;
}

.main-container {
  display: flex;
  flex-direction: column;
  background-color: #fff;
  box-shadow: 0 0 8px 4px rgba(0, 77, 153, 0.1);
}

.main-header {
  padding: .5rem 0;
  font-weight: bold;
  user-select: none;
}

.main-body {
  max-height: 30rem;
  overflow-y: auto;
}


.hash-link {
  display: block;
  position: relative;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  text-decoration: none;
  color: #666;
  padding: .5rem;
  font-size: .8rem;
}

.hash-link:hover{
  background-color: #f3f9ff;
  color: #06c;
}

.hash-link.active{
  color: #1e80ff;
}

.hash-link.active:before {
  content: "";
  position: absolute;
  left: 0;
  width: 4px;
  height: 1rem;
  background: #1e80ff;
  border-radius: 0 4px 4px 0;
}

.hash-link.h2 {
  padding-left: .75rem;
}

.hash-link.h3 {
  padding-left: 1rem;
}

.hash-link.h4 {
  padding-left: 1.25rem;
}

.hash-link.h5 {
  padding-left: 1.5rem;
}

@media screen and (max-device-width: 1280px){

}
</style>
