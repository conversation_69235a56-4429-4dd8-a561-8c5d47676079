<script setup>
import { computed, watch } from 'vue'
import { hexToRgbArray, rgbToHsl, hslToRgbString } from '../utils/color.js'

const props = defineProps({
  visible: <PERSON><PERSON><PERSON>,
  note: Object
})

const emit = defineEmits(['close'])

const formatTime = (timestamp) => {
  if (!timestamp) return '';
  const date = new Date(timestamp)
  const now = new Date()
  const diff = now - date
  
  if (diff < 60000) return '刚刚'
  if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`
  if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`
  if (diff < 2592000000) return `${Math.floor(diff / 86400000)}天前`
  return date.toLocaleDateString()
}

const dialogDynamicStyles = computed(() => {
  if (!props.note) return {};
  const hexColor = props.note.style || '#a2e1d4';
  const [r, g, b] = hexToRgbArray(hexColor);
  const [h, s, l] = rgbToHsl(r, g, b);
  const isLight = l > 0.7;

  const baseRgb = `${r}, ${g}, ${b}`;
  const elementLightness = isLight ? Math.max(0, l - 0.5) : Math.min(1, l + 0.5);
  const elementRgb = hslToRgbString(h, s, elementLightness);

  return {
    '--text-color': `rgba(${elementRgb}, 0.9)`,
    '--text-color-medium': `rgba(${elementRgb}, 0.7)`,
    '--text-color-light': `rgba(${elementRgb}, 0.5)`,
    '--border-color': `rgba(${elementRgb}, 0.25)`,
    '--bg-color-light': `rgba(${elementRgb}, 0.1)`,
    'backgroundColor': `rgba(${baseRgb}, 0.6)`
  };
});

const closeModal = () => {
  emit('close')
}


</script>

<template>
  <Transition name="modal-fade">
    <div v-if="visible" class="fixed inset-0 flex items-center justify-center z-50 p-4 backdrop-blur-sm" @click.self="closeModal">
      <div v-if="note" class="dialog-content rounded-2xl w-full max-w-lg max-h-[90vh] flex flex-col shadow-2xl backdrop-blur-xl" :style="dialogDynamicStyles">
        <div class="p-6 border-b" :style="{ borderColor: 'var(--border-color)' }">
          <div class="flex justify-between items-center">
            <h2 class="text-2xl font-bold" :style="{ color: 'var(--text-color)' }">查看思绪</h2>
            <button @click="closeModal" class="transition-opacity rounded-full p-1" :style="{ color: 'var(--text-color-medium)' }">
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg>
            </button>
          </div>
        </div>
        
        <div class="p-6 space-y-4 overflow-y-auto">
          <p class="content-text">{{ note.content }}</p>
          
          <div v-if="note.tags && note.tags.length" class="flex flex-wrap gap-2">
            <span v-for="tag in note.tags" :key="tag" class="tag-item">#{{ tag }}</span>
          </div>
        </div>

        <div class="p-6 mt-auto text-xs" :style="{ color: 'var(--text-color-medium)' }">
          <span>{{ note.username || '匿名' }}</span>
          <span> · </span>
          <span>{{ formatTime(note.modifyTime) }}</span>
        </div>
      </div>
    </div>
  </Transition>
</template>

<style scoped>
.dialog-content {
  transition: background-color 0.3s ease-in-out;
}
.content-text {
  color: var(--text-color);
  line-height: 1.7;
  white-space: pre-wrap;
  word-wrap: break-word;
  font-size: 1rem;
}
.tag-item {
  display: inline-flex;
  align-items: center;
  font-size: 0.75rem;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  background-color: var(--bg-color-light);
  color: var(--text-color-medium);
}

.modal-fade-enter-active,
.modal-fade-leave-active {
  transition: opacity 0.3s ease;
}
.modal-fade-enter-from,
.modal-fade-leave-to {
  opacity: 0;
}
.modal-fade-enter-active .dialog-content,
.modal-fade-leave-active .dialog-content {
  transition: transform 0.3s ease, background-color 0.3s ease-in-out;
}
.modal-fade-enter-from .dialog-content,
.modal-fade-leave-to .dialog-content {
  transform: scale(0.95) translateY(20px);
}
</style>