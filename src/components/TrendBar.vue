<script setup>
import AppLink from "./AppLink.vue";
import SkeletonItem from './SkeletonItem.vue';


defineProps({
  dataList: Array
})


</script>

<template>

  <div class="main-container">
    <div class="header ">
   
      <svg t="1749526272498" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="31025" width="20" height="20"><path d="M132.6 639c9.4 0 18.8-3.6 26-10.3l261.8-245.2 148.8 114.3c15.7 11.7 36.8 9.9 50.2-3.6l231.7-234.9v55.6c0 21.1 17 38.1 38.1 38.1s38.1-17 38.1-38.1V160.3c0-11.2-4.5-21.5-13-28.7-8.1-7.2-18.8-10.3-29.6-9L713 141.9c-21.1 2.2-35.9 21.1-33.6 42.1 2.2 21.1 21.1 35.9 42.1 33.6l71.7-8.1L588.4 418 440.1 302.8c-14.8-10.8-35.4-9.4-48.9 2.7L107 573.6c-7.6 6.7-11.7 16.1-12.1 26.4-0.4 10.3 3.1 19.7 10.3 27.3 7.2 7.7 17.1 11.7 27.4 11.7z m789.7 186.5h-9v0.4h-97.7v-372c0-21.1-17-38.1-38.1-38.1s-38.1 17-38.1 38.1v371.6h-100V599.6c0-20.6-17-37.7-38.1-37.7s-38.1 17-38.1 37.7v225.9h-100V541.3c0-21.1-17-38.1-38.1-38.1s-38.1 17-38.1 38.1v284.2H287V658.3c0-20.6-17-37.7-38.1-37.7s-38.1 17-38.1 37.7v167.2H102.1c-21.1 0-38.1 17-38.1 38.1s17 38.1 38.1 38.1h819.8c21.1 0 38.1-17 38.1-38.1s-16.6-38.1-37.7-38.1z" p-id="31026" fill="#515151"></path></svg>

      <span class="ml-2 title" >热点趋势</span>
    </div>
    
    <div class="content">
      <AppLink class="link" v-for="(link, index) in dataList" :to="link.link" :title="link.title"  >
        <span class="link-index" >{{index + 1}}.</span>
        <span class="link-title">{{link.title}}</span>
        <span class="link-extra">{{link.extra}}</span>
      </AppLink>
      <SkeletonItem v-if="!dataList.length" :rows="6"/>
    </div>
    


  </div>



</template>

<style scoped>


.main-container {
  display: flex;
  flex-direction: column;
  background-color: #fff;
  padding: .5rem;
}

.header {
  display: flex;
  user-select: none;
  justify-content: left;
  align-items: center;
  padding: .5rem;
  background: linear-gradient(90deg,hsla(48, 97%, 85%, .3), hsla(0, 0%, 97%, 0));
}


.title {
  font-size: 14px;
  font-weight: bold;
  margin-right: .5rem;
}


.content {
  display: flex;
  flex-direction: column;
  height: 20rem;
  overflow-y: auto;
  padding-right: .5rem;
  padding-left: .5rem;
}

.link:hover .link-title {
  color: #3071f2;
}

.link {
  display: flex;
  text-decoration: none;
  justify-content: space-between;
  align-items: center;
  line-height: 2rem;
  color: #333;
  font-size: .75rem;
}

.content .link-index {
  font-weight: bold;
  margin-right: 4px;
   color: #999;
}

.content .link:first-child .link-index {
  color: #cc3939;
}

.content .link:nth-child(2) .link-index {
  color: #de6b30;
}

.content .link:nth-child(3) .link-index  {
  color: #cc984f;
}

.link .link-index, .link .link-extra {
  flex-grow: 0;
  flex-shrink: 0;
}

.link-extra {
  font-size: .75rem;
  color: #666;
}

.link .link-title {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  flex: 1;
  margin: 0 .25rem;
}



</style>
