<script setup>
import { computed } from 'vue';

const props = defineProps({
  visible: <PERSON><PERSON><PERSON>,
  message: String,
  large: Boolean,
});

// --- 改动核心 ---
// 1. 将通用样式提取出来，并为两种模式都添加 backdrop-blur-xl
const toastClasses = computed(() => [
  'fixed z-[1001] transition-all duration-300 backdrop-blur-xl', // 通用样式
  props.large
    ? 'w-11/12 max-w-md -translate-x-1/2 -translate-y-1/2 top-1/3 left-1/2 p-6 rounded-2xl shadow-2xl'
    : 'bottom-10 left-1/2 -translate-x-1/2 px-6 py-3 rounded-full shadow-lg'
]);

// 2. 统一使用 Dialog 的颜色变量和风格
const toastStyles = computed(() => {
  const isLarge = props.large;
  return {
    // 使用与 Dialog 完全相同的背景色
    'backgroundColor': 'rgba(30, 30, 30, 0.6)',
    
    // (关键) 为 large 模式添加与 Dialog 相同的边框，增强质感
    'border': isLarge ? '1px solid rgba(255, 255, 255, 0.25)' : 'none',
    
    // 定义文字颜色变量
    '--text-color': 'rgba(255, 255, 255, 0.9)',
    
    // 定义动画变量
    '--transform-from': isLarge ? 'translateY(0) scale(0.95)' : 'translateY(20px)',
  };
});
</script>

<template>
  <Teleport to="body">
    <Transition name="toast-fade">
      <div v-if="visible" :class="toastClasses" :style="toastStyles">
        <p class="text-center font-medium" :style="{ color: 'var(--text-color)' }">
          {{ message }}
        </p>
      </div>
    </Transition>
  </Teleport>
</template>

<style scoped>
.toast-fade-enter-active,
.toast-fade-leave-active {
  transition: opacity 0.3s ease, transform 0.3s ease;
}
.toast-fade-enter-from,
.toast-fade-leave-to {
  opacity: 0;
  transform: var(--transform-from); /* 动画依然由 CSS 变量驱动 */
}
</style>