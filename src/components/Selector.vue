<script setup>
import {ref, computed, defineProps, toRefs, onMounted, defineEmits } from 'vue'

const props = defineProps({
  name: String,
  modelValue: String,
  options: Array
})

const emit = defineEmits(['update:modelValue', 'change'])

const { options, modelValue, name } = toRefs(props);

let optionWidth = ref(0)
const dropdown = ref(false)
const optionGroup = ref(null)

onMounted(() => {
  optionWidth.value = optionGroup.value.clientWidth
})

let selected = (value) => {
  emit('update:modelValue', value)
  emit('change', value)
  dropdown.value = false
}

const label = computed(() => {
  let currOption = options.value.find(option => option.value === modelValue.value);
  return currOption && currOption.name || options.value[0].value.name
})
</script>

<template>
  <select :name="name" v-model="modelValue" hidden>
    <option v-for="option in options" :value="option.value">{{option.name}}</option>
  </select>
  <div class="selector"  @mouseover="dropdown = true" @mouseleave="dropdown = false" :style="{ width: optionWidth + 'px'}">
    <div class="label">{{ label }}</div>
    <div class="dropdown-arrow"></div>
    <div ref="optionGroup" class="option-group" :style="{visibility: dropdown ? 'visible': 'hidden' }">
      <div class="option" :class="option.value === modelValue ? 'selected' : ''" v-for="option in options" :value="option.value" v-on:click="selected(option.value)">{{option.name}}</div>
    </div>
  </div>


</template>

<style scoped>

.selector{
  font-size: 14px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  text-align: center;
  cursor: pointer;
  color: #777;
  padding: .5rem 0;
}


.selector:hover .label{
  color: #06c;
}

.selector:hover .dropdown-arrow{
  border-color: #06c transparent;
}

.selector .dropdown-arrow{
  border-color: #70757a transparent;
  border-style: solid;
  border-width: 5px 4px 0 4px;
  margin-left: .5rem;
  width: 0;
  height: 0;
}


.selector .option-group {
  position: absolute;
  z-index: 99;
  /**border: 1px solid #e1e1e1;**/
  box-shadow:  0 0 8px rgba(0, 0, 0, 0.08);
  margin-top: -.75rem;
  top: calc(100% + .75rem);
  word-break: keep-all;
  background-color: #fff;
}



.selector .option-group .option {
  padding: .25rem 1rem;
}

.selector .option-group .option.selected {
  color: #06c;
}

.selector .option-group .option:hover {
  background-color:#f3f9ff;
}
</style>
