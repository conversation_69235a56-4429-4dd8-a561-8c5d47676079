<script setup>
import {ref, computed, defineProps, toRefs, onMounted, defineEmits } from 'vue'

const props = defineProps({
  modelValue: Boolean,
})

const emit = defineEmits(['update:modelValue'])

const { modelValue } = toRefs(props);


const toggle = () => {
  let value = !modelValue.value
  emit('update:modelValue', value)
}

</script>

<template>

  <div class="switch" :class="modelValue? 'switch-on': ''" v-on:click="toggle()" >
    <div class="switch-block" data-on="开启" data-off="关闭"></div>
    <p class="switch-space"><span>占位占位</span></p>
  </div>

</template>

<style scoped>

.switch {
  position: relative;
  display: inline-block;
  font-size: 12px;
  padding: 2px 10px;
  margin: 2px 0;
  border-radius: 2px;
  box-shadow: 0 0 0 1px #ccc;

  overflow: hidden;
  cursor: pointer;
  vertical-align: middle;
  user-select: none;
  transition: box-shadow .3s;
}


.switch-block {
  position: absolute;
  width: 50%;
  height: 100%;
  left: 0;
  top: 0;
  background-color: #fff;
  transform: translateX(0);
  transition: transform .3s;
}

.switch-on > .switch-block{
  transform: translateX(100%);
}

.switch-block:before,.switch-block:after {
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  color: #fff;
}

.switch-block:before {
  content: attr(data-on);
  left: -100%;
  color: #fff;
  background-color: #337ab7;
}


.switch-block:after {
  content: attr(data-off);
  left: 100%;
  background-color: #bbb;
}

</style>
