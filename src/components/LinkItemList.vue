<script setup>
import AppLink from "./AppLink.vue";

defineProps({
  linkList: {
    type: Array,
    default: () => []
  }
})
</script>

<template>
  <div class="flex-container">
    <div class="link-item" v-for="link in linkList">
      <AppLink :to="link.url">
        {{ link.title }}
      </AppLink>
    </div>
  </div>
</template>

<style scoped>
.flex-container {
  display: flex;
  flex-direction: column;
  margin-top: 1rem;
  flex: 1;
}

.link-item {
  padding: 1rem;
  border-bottom: 1px solid #f3f3f3;
}

.link-item:last-child {
  border-bottom: none;
}

.link-item a {
  color: #06c;
  text-decoration: none;
  font-size: 0.9rem;
}

.link-item a:hover {
  text-decoration: underline;
}
</style>
