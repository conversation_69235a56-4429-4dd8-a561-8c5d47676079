<script setup>

import NotAvailable from "../pages/NotAvailable.vue";
import MainLayout from "./MainLayout.vue";
import SlaveLayout from "./SlaveLayout.vue";

import { ref, defineProps, provide } from 'vue'

defineProps({
  spacing: {
    type: Boolean,
    default: true
  },
  available: {
    type: Boolean,
    default: true
  }
})





</script>

<template>
  <div v-show="available" class="relative flex flex-col md:flex-row md:justify-between md:items-start min-h-screen min-w-0" >
    <div class="flex flex-auto min-w-0">
      <MainLayout :spacing="spacing">
        <slot />
      </MainLayout>
    </div>
    <div class="flex md:sticky md:top-[5rem] flex-none">
      <SlaveLayout :spacing="spacing">
        <slot name="slider"/>
      </SlaveLayout>
    </div>
  </div>
  <NotAvailable v-show="!available"/>

</template>

