<template>
  <div
    ref="containerRef"
    class="w-full overflow-hidden"
    @mouseenter="handleMouseEnter"
    @mouseleave="handleMouseLeave"
  >
    <!-- 
      - 通过 :style 动态绑定 transition 和 transform
      - 当需要瞬间跳转时，我们会临时禁用 transition
    -->
    <div
      ref="wrapperRef"
      class="scroll-wrapper"
      :style="{
        transform: `translateY(${translateY}px)`,
        transition: transitionEnabled ? `transform ${transitionDuration}ms ease-in-out` : 'none'
      }"
    >
      <div
        v-for="(item, index) in displayItems"
        :key="`scroll-item-${index}`"
        class="scroll-item"
      >
        <slot :item="item" :index="index % originalItemCount">
          <AppLink class="scroll-link" :to="item.link">{{ item.title || item }}</AppLink>
        </slot>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onBeforeUnmount, nextTick, watch } from 'vue';
import AppLink from './AppLink.vue';

// --- Props Definition ---
const props = defineProps({
  items: {
    type: Array,
    required: true,
    default: () => [],
  },
  // 【新】每行停留的时间 (毫秒)
  stayDuration: {
    type: Number,
    default: 3000,
  },
  // 【新】滚动到下一行的动画时间 (毫秒)
  transitionDuration: {
    type: Number,
    default: 500,
  },
  pauseOnHover: {
    type: Boolean,
    default: true,
  }
});

// --- DOM Refs and Reactive State ---
const containerRef = ref(null);
const wrapperRef = ref(null);
const itemHeight = ref(0);
const translateY = ref(0);
const currentIndex = ref(0); // 当前显示的是第几行
const transitionEnabled = ref(true); // 控制是否启用 CSS 过渡动画
let intervalId = null;

// --- Computed Properties ---
const originalItemCount = computed(() => props.items.length);

// 只有超过一项时才复制内容
const displayItems = computed(() => {
  if (originalItemCount.value < 2) return props.items;
  // 【修复】在列表末尾添加第一项，实现从最后到最初的平滑过渡
  return [...props.items, props.items[0]];
});

// --- Core Logic ---

const initialize = async () => {
  stopScroll();
  await nextTick();

  const wrapperEl = wrapperRef.value;
  if (!wrapperEl || !wrapperEl.children.length || originalItemCount.value < 2) {
    if (containerRef.value) containerRef.value.style.height = 'auto';
    return;
  }
  
  const firstItem = wrapperEl.children[0];
  itemHeight.value = firstItem.offsetHeight;

  if (itemHeight.value > 0) {
    containerRef.value.style.height = `${itemHeight.value}px`;
    Array.from(wrapperEl.children).forEach(child => {
      child.style.height = `${itemHeight.value}px`;
    });
  }

  // 重置到初始状态
  currentIndex.value = 0;
  translateY.value = 0;
  
  startScroll();
};

const moveToNext = () => {
  currentIndex.value++;
  translateY.value = -currentIndex.value * itemHeight.value;

  // 【关键】处理循环
  if (currentIndex.value >= originalItemCount.value) {
    // 当滚动到复制的最后一项（即原始第一项）时，
    // 等待滚动动画结束
    setTimeout(() => {
      // 1. 禁用动画，瞬间跳回起点
      transitionEnabled.value = false;
      currentIndex.value = 0;
      translateY.value = 0;
      
      // 2. 强制浏览器渲染此帧
      // 使用 nextTick 或 requestAnimationFrame 确保 DOM 更新后再启用动画
      nextTick(() => {
        // 3. 恢复动画，为下一次滚动做准备
        transitionEnabled.value = true;
      });
    }, props.transitionDuration);
  }
};


// --- Control Functions ---
const startScroll = () => {
  stopScroll(); // 确保没有正在运行的定时器
  if (originalItemCount.value > 1) {
    intervalId = setInterval(moveToNext, props.stayDuration);
  }
};

const stopScroll = () => {
  if (intervalId) {
    clearInterval(intervalId);
    intervalId = null;
  }
};

const handleMouseEnter = () => {
  if (props.pauseOnHover) stopScroll();
};

const handleMouseLeave = () => {
  if (props.pauseOnHover) startScroll();
};

// --- Lifecycle Hooks ---
onMounted(() => {
  initialize();
});

onBeforeUnmount(() => {
  stopScroll();
});

watch(() => props.items, () => {
  initialize();
}, { deep: true });

</script>

<style scoped>
.scroll-link {
  text-decoration: none;
  color: #333;
}

.scroll-item {
  display: flex;
  align-items: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>