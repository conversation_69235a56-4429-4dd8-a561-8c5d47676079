<script setup>
import { computed, watch, onMounted } from 'vue'
import { RouterLink, useRoute, useRouter } from 'vue-router' // 导入 useRouter

defineOptions({
  inheritAttrs: false,
})

const props = defineProps({
  // 如果使用 TypeScript，请添加 @ts-ignore
  ...RouterLink.props,
  inactiveClass: String,
})

const route = useRoute();
const router = useRouter(); // 获取 router 实例

const isExternalLink = computed(() => {
  return typeof props.to === 'string' && (props.to.startsWith('http') || props.to.startsWith('//'))
})

// 检查是否为哈希链接
const isHashLink = computed(() => {
  return typeof props.to === 'string' && props.to.startsWith('#')
})

// 处理哈希链接点击
const handleHashLinkClick = (event) => {
  if (isHashLink.value) {
    event.preventDefault();

    // 获取目标元素ID（去掉#前缀）
    const targetId = props.to.substring(1);

    // 尝试查找目标元素 - 先尝试直接使用ID
    let targetElement = document.getElementById(targetId);

    // 如果没找到，尝试查找带有name属性的元素
    if (!targetElement) {
      targetElement = document.getElementsByName(targetId)[0];
    }

    // 尝试查找GitHub风格的锚点ID (user-content- 前缀)
    if (!targetElement) {
      targetElement = document.getElementById('user-content-' + targetId);
    }

    // 尝试查找标题元素
    if (!targetElement) {
      // 查找所有h1-h6标题
      const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6');
      for (const heading of headings) {
        // 检查标题文本是否匹配（忽略大小写和特殊字符）
        const headingText = heading.textContent.trim().toLowerCase().replace(/[^\w\s-]/g, '').replace(/\s+/g, '-');
        if (headingText === targetId.toLowerCase() || headingText === targetId.toLowerCase().replace(/-/g, '')) {
          targetElement = heading;
          break;
        }
      }
    }

    // 仅使用 router.push 来更新 URL 和触发 scrollBehavior
    // 不再手动查找元素或调用 window.scrollTo
    router.push(props.to);

  
  }
}


</script>

<template>
  <!-- 外部链接 -->
  <a v-if="isExternalLink" v-bind="$attrs" :href="to" target="_blank">
    <slot />
  </a>

  <!-- 哈希链接 -->
  <a
    v-else-if="isHashLink"
    v-bind="$attrs"
    :href="to"
    @click="handleHashLinkClick"
    :class="to === route.hash ? activeClass : inactiveClass"
  >
    <slot />
  </a>

  <!-- 内部路由链接 -->
  <router-link
    v-else
    v-bind="$props"
    custom
    v-slot="{ isActive, href, navigate }"
  >
    <a
      v-bind="$attrs"
      :href="href"
      @click="navigate"
      :class="isActive ? activeClass : inactiveClass"
    >
      <slot />
    </a>
  </router-link>
</template>


