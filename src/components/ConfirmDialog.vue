<script setup>
import { computed } from 'vue';

const props = defineProps({
  visible: Boolean,
  title: String,
  message: String,
});

const emit = defineEmits(['confirm', 'cancel']);

const dialogStyles = computed(() => ({
  '--text-color': 'rgba(255, 255, 255, 0.9)',
  '--text-color-medium': 'rgba(255, 255, 255, 0.7)',
  '--border-color': 'rgba(255, 255, 255, 0.25)',
  '--bg-color-light': 'rgba(255, 255, 255, 0.1)',
  '--bg-color-hover': 'rgba(255, 255, 255, 0.2)',
  '--primary-bg-color': 'rgba(255, 255, 255, 0.25)',
  '--primary-bg-hover': 'rgba(255, 255, 255, 0.35)',
  'backgroundColor': 'rgba(30, 30, 30, 0.6)',
}));

const confirm = () => {
  emit('confirm');
};

const cancel = () => {
  emit('cancel');
};
</script>

<template>
  <Transition name="modal-fade">
    <div v-if="visible" class="fixed inset-0 flex items-center justify-center z-50 p-4 backdrop-blur-sm" @click.self="cancel">
      <div class="dialog-content rounded-2xl w-full max-w-sm flex flex-col shadow-2xl backdrop-blur-xl" :style="dialogStyles">
        <div class="p-6">
          <h2 class="text-xl font-bold text-center" :style="{ color: 'var(--text-color)' }">{{ title }}</h2>
        </div>
        
        <div class="px-6 pb-6 text-center" :style="{ color: 'var(--text-color-medium)' }">
          <p>{{ message }}</p>
        </div>

        <div class="p-4 border-t flex gap-4" :style="{ borderColor: 'var(--border-color)' }">
          <button @click="cancel" class="btn-secondary w-full">取消</button>
          <button @click="confirm" class="btn-primary w-full">确认</button>
        </div>
      </div>
    </div>
  </Transition>
</template>

<style scoped>
.dialog-content {
  transition: background-color 0.3s ease-in-out;
}
.btn-primary, .btn-secondary {
  font-weight: 700;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  transition: background-color 0.2s;
  cursor: pointer;
}
.btn-primary {
  background-color: var(--primary-bg-color);
  color: white;
}
.btn-primary:hover {
  background-color: var(--primary-bg-hover);
}
.btn-secondary {
  background-color: var(--bg-color-light);
  color: var(--text-color);
}
.btn-secondary:hover {
  background-color: var(--bg-color-hover);
}

.modal-fade-enter-active,
.modal-fade-leave-active {
  transition: opacity 0.3s ease;
}
.modal-fade-enter-from,
.modal-fade-leave-to {
  opacity: 0;
}
.modal-fade-enter-active .dialog-content,
.modal-fade-leave-active .dialog-content {
  transition: transform 0.3s ease, background-color 0.3s ease-in-out;
}
.modal-fade-enter-from .dialog-content,
.modal-fade-leave-to .dialog-content {
  transform: scale(0.95) translateY(20px);
}
</style>