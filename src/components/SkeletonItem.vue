<script setup>
defineProps({
  rows: {
    type: Number,
    default: 2
  }
})
</script>

<template>
  <div class="skeleton-container">
    <div class="skeleton-title"></div>
    <div class="skeleton-link"></div>
    <div class="skeleton-desc"></div>
    <div
      v-for="i in rows"
      :key="i"
      class="skeleton-text-row"
      :class="{ 'skeleton-text-row-short': i === rows }"
    ></div>
  </div>
</template>

<style scoped>
.skeleton-container {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  padding: 1rem;
  background-color: #fff;
}

.skeleton-title, .skeleton-link, .skeleton-desc, .skeleton-text-row {
  background: linear-gradient(90deg, #f2f2f2 25%, #e6e6e6 37%, #f2f2f2 63%);
  background-size: 400% 100%;
  animation: skeleton-loading 1.4s ease infinite;
  border-radius: 4px;
}

.skeleton-title {
  height: 2rem;
  width: 60%;
}

.skeleton-link {
  height: 1rem;
  width: 40%;
}

.skeleton-desc {
  height: 1rem;
  width: 90%;
}

.skeleton-text-row {
  height: 1rem;
  width: 100%;
}

.skeleton-text-row-short {
  width: 70%;
}

@keyframes skeleton-loading {
  0% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0 50%;
  }
}
</style>
