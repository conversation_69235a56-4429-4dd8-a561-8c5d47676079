<script setup>
import { ref, inject } from 'vue'

const props = defineProps({
  visible: <PERSON>ole<PERSON>
})

const emit = defineEmits(['close', 'submit'])

let http = inject('http')
let loading = ref(false)
let linkTitle = ref('')
let linkUrl = ref('')
let warning = ref('')

const closeDialog = () => {
  linkTitle.value = ''
  linkUrl.value = ''
  warning.value = ''
  emit('close')
}

const addLink = async () => {
  if(loading.value) {
    return
  }

  if(!linkTitle.value || linkTitle.value.length > 10) {
    warning.value = '名称长度1-10个字符'
    return
  }

  if(!linkUrl.value || !linkUrl.value.startsWith('http://') && !linkUrl.value.startsWith('https://')) {
    warning.value = '链接请添加`http`前缀'
    return
  }

  try {
    loading.value = true
    warning.value = ''
    let resp = await http.post('/link/add', { title: linkTitle.value, link: linkUrl.value })
    warning.value = resp.data.message
    loading.value = false
    emit('submit', resp.data)
  } catch (e) {
    warning.value = e.message
    loading.value = false
  }
}
</script>

<template>
  <div class="dialog" v-show="visible" @click.self="closeDialog">
    <div class="dialog-wrap">
      <div class="header">
        <div class="title">链接申请收录</div>
      </div>
      <div class="dialog-content">
        <div class="dialog-form">
          <input type="text" placeholder="名称" v-model="linkTitle"/>
          <input type="text" placeholder="网址" v-model="linkUrl"/>
          <a class="btn" v-on:click="addLink()">{{loading && '网址提交中,请耐心等待...' || '快速提交（免审核）'}}</a>
          <span class="warning" v-html="warning"></span>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.dialog {
  z-index: 100;
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  display: flex;
  justify-content: center;
  align-items: center;
}

.dialog-wrap {
  overflow: hidden;
  background-color: #fff;
  box-shadow: 0 0 2rem rgba(0,0,0, .08);
  padding: 1rem 2rem 2rem 2rem;
  opacity: .95;
}

.dialog-form {
  display: flex;
  flex-direction: column;
  width: 20rem;
  overflow: hidden;
}

.dialog .header {
  user-select: none;
  text-align: center;
  margin: 1rem .25rem;
}

.dialog .header .title {
  color: #666;
  font-size: .75rem;
}

input[type="text"] {
  display: block;
  background-color: rgba(242, 248, 255, 0.5);
  padding: 0.5rem 1rem;
  margin-bottom: 1rem;
  border: none;
  color: #777;
  outline: none;
}

.warning {
  color: #cc3939;
  font-size: .75rem;
}

.btn {
  text-decoration: none;
  line-height: 2rem;
  margin: 0 .25rem;
  color: #666;
  font-size: .75rem;
  cursor: pointer;
  user-select: none;
  align-self: flex-start;
}
</style>
