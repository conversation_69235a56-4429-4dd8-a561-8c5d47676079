<script setup>
import { ref,defineEmits, watch, onMounted, defineExpose } from 'vue'

let props = defineProps({
  url: String
})

let finish = false
let loading = false
let suspend = false
let totalChunks = [] // To hold fetched chunks
let scrollHeightList = [] // each chunk's scroll height

let content = ref('')
let container = ref(null)

let scrollHeightJson = localStorage.getItem("scroll_height_" + props.url)

if(scrollHeightJson) {
  scrollHeightList = JSON.parse(scrollHeightJson);
}

let offset = parseInt(localStorage.getItem("offset_bytes_" + props.url)) || 0

let start = offset, chunkSize = 512000, totalSize;

let end = start + chunkSize - 1;


//已被翻页的距离
let startScrollTop = () => {
  let startDistance = 0;
  let offsetIndex = Math.floor(offset/chunkSize);
  let startIndex = Math.floor(start/chunkSize);
  for(let i = offsetIndex; i < startIndex - 1 && i < scrollHeightList.length; i++) {
    startDistance += scrollHeightList[i]
  }
  return startDistance;
}

//计算高度对应跳过的字节数
let calculateOffsetPosition = (containerScrollTop) => {

  //末页首高度
  let startDistance = 0;
  let scrollHeight = scrollHeightList[0] || 0;
  let offsetIndex = Math.floor(offset/chunkSize);
  let startIndex = Math.floor(start/chunkSize);

  let offsetBytes = offsetIndex * chunkSize;
  for(let i = offsetIndex; i < startIndex - 1 && i < scrollHeightList.length; i++) {

    scrollHeight = scrollHeightList[i]

    if(startDistance + scrollHeightList[i] > containerScrollTop) {
      break;
    }

    startDistance += scrollHeightList[i]
    offsetBytes += chunkSize
  }
  return {
    scrollHeight,
    scrollTop: containerScrollTop - startDistance,
    offsetBytes
  }
}

let loadMore = async () => {
  if(finish || loading) {
    return;
  }

  loading = true

  try {
    const headers = new Headers();
    headers.append('Range', `bytes=${start}-${end}`);

    try {
      let response = await fetch(props.url, {
        method: 'GET',
        headers: headers,
        mode: 'cors'
      })

      totalChunks.push(await response.blob())

      const completeBlob = new Blob(totalChunks);

      // Convert to text if needed, or work with the blob as a file
      content.value = await completeBlob.text();


      // Check if we've reached the end of the file
      const contentRange = response.headers.get('Content-Range');

      totalSize = parseInt(contentRange.split('/')[1]);
      if (end >= totalSize - 1) {
        finish = true;
      }

      start = end + 1;
      end += chunkSize;


      scrollHeightList[Math.floor(start/chunkSize) - 1] = container.value.scrollHeight - startScrollTop()

      localStorage.setItem("scroll_height_" + props.url, JSON.stringify(scrollHeightList))
    } catch(e) {
      content.value = e.message;
      finish = true
    }

  } finally {
    loading = false
  }
}


let updateScroll = () => {

  let position = calculateOffsetPosition(container.value.scrollTop)

  localStorage.setItem("scroll_top_" + props.url, position.scrollTop)

  localStorage.setItem("offset_bytes_" + props.url, position.offsetBytes)

  let readPercent = Math.ceil((container.value.scrollTop + container.value.clientHeight )/(container.value.scrollHeight)*100);

  if(readPercent > 98) {
    loadMore()
  }


  let percent = Math.ceil(position.offsetBytes/totalSize*100 + position.scrollTop/position.scrollHeight*100*chunkSize/totalSize);

  if(finish) {
    percent = readPercent
  }
  //document.getElementById('progress').innerText = '页首 ' + percent + "%"
}

function pageScroll() {

  if(!suspend) {
    container.value.scrollBy(0,1);
    updateScroll()
  }
  setTimeout(pageScroll,60);
}


const scrollTop = () => {
  start = 0;
  finish = false;
  end = start + chunkSize - 1;
  container.value.scrollTop = 0;
  localStorage.setItem("offset_bytes_" + props.url, 0)
  offset = 0;
  totalChunks.length = 0
  loadMore()
  updateScroll()
}

defineExpose({
  scrollTop
})

onMounted(async () => {
  await loadMore()

  container.value.addEventListener("click", function(event){
    suspend = !suspend
  })

  container.value.addEventListener('mouseleave', () => {
    suspend = true
  })

  container.value.addEventListener('mouseenter', () => {
    suspend = false
  })

  container.value.addEventListener("scroll", updateScroll);

  let progress = parseInt(localStorage.getItem("scroll_top_" + props.url))

  if(progress){
    container.value.scrollTop = progress;
    updateScroll()
  }

  pageScroll()



})


</script>

<template>
  <div ref="container" class="book-container">
    <pre v-html="content"></pre>

  </div>
</template>

<style scoped>

  .book-container pre {
    color: #555;
    user-select: none;
    line-height: 2rem;
    font-size: 12px;
    white-space: pre-wrap;
    word-wrap: break-word;
    font-family: "Microsoft YaHei", sans-serif !important;
  }

  .icon {
    position: absolute;
    right: .25rem;
    bottom: .25rem;
  }
</style>
