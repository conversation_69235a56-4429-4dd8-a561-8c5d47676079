<script setup>
import { ref, computed, watch, inject, reactive } from 'vue'
import useDialog from '../utils/useDialog.js'

const props = defineProps({
  visible: Boole<PERSON>,
  note: Object,
  user: Object
})

const emit = defineEmits(['close', 'submit'])

const { alert } = useDialog()

const noteForm = reactive({
  content: '',
  tags: [],
  style: '#a2e1d4',
  username: ''
})
const newTag = ref('')

const styleOptions = [
  '#a2e1d4', '#acf6ef', '#cbf5fb', '#bdf3d4',
  '#e6e2c3', '#e3c887', '#fad8be', '#fbb8ac',
  '#fe6673', '#d7d4f0'
]

const modalTitle = computed(() => props.note ? '编辑思绪' : '记录此刻')
const modalSubmitText = computed(() => props.note ? '保存' : '发布')

import { hexToRgbArray, rgbToHsl, hslToRgbString } from '../utils/color.js'

const dialogDynamicStyles = computed(() => {
  const [r, g, b] = hexToRgbArray(noteForm.style);
  const [h, s, l] = rgbToHsl(r, g, b);
  const isLight = l > 0.7;

  const baseRgb = `${r}, ${g}, ${b}`;
  // Adjust lightness for text and elements to ensure contrast
  const elementLightness = isLight ? Math.max(0, l - 0.5) : Math.min(1, l + 0.5);
  const elementRgb = hslToRgbString(h, s, elementLightness);

  return {
    '--text-color': `rgba(${elementRgb}, 0.9)`,
    '--text-color-medium': `rgba(${elementRgb}, 0.7)`,
    '--text-color-light': `rgba(${elementRgb}, 0.5)`,
    '--border-color': `rgba(${elementRgb}, 0.25)`,
    '--border-color-hover': `rgba(${elementRgb}, 0.4)`,
    '--bg-color-light': `rgba(${elementRgb}, 0.1)`,
    '--bg-color-hover': `rgba(${elementRgb}, 0.2)`,
    '--ring-color': `rgba(${elementRgb}, 0.5)`,
    'backgroundColor': `rgba(${baseRgb}, 0.6)`
  };
});

watch(() => props.visible, (newVal) => {
  if (newVal) {
    if (props.note) {
      noteForm.content = props.note.content;
      noteForm.tags = [...(props.note.tags || [])];
      noteForm.style = props.note.style || '#a2e1d4';
      noteForm.username = props.note.username || '';
    } else {
      resetForm();
      if (props.user) {
        noteForm.username = props.user.name;
      }
    }
  } 
}, { immediate: true });

const resetForm = () => {
  noteForm.content = '';
  noteForm.tags = [];
  noteForm.style = styleOptions[Math.floor(Math.random() * styleOptions.length)];
  noteForm.username = '';
  newTag.value = ''
}

const addTag = () => {
  const tag = newTag.value.trim()
  if (tag && !noteForm.tags.includes(tag)) {
    if (noteForm.tags.length >= 2) {
      alert('提示', '最多添加2个标签');
      return
    }
    noteForm.tags.push(tag)
    newTag.value = ''
  }
}

const removeTag = (index) => {
  noteForm.tags.splice(index, 1)
}

const closeModal = () => {
  emit('close')
}

const submitNote = () => {
  if (!noteForm.content.trim()) {
    alert('提示', '请输入笔记内容');
    return
  }
  if (noteForm.content.length > 1000) {
    alert('提示', '笔记内容不能超过1000个字符');
    return
  }
  emit('submit', noteForm)
}
</script>

<template>
  <Transition name="modal-fade">
    
    <div v-if="visible" class="fixed inset-0 flex items-center justify-center z-50 p-4 backdrop-blur-sm" @click.self="closeModal">
      <div
        class="dialog-content rounded-2xl w-full max-w-lg max-h-[90vh] flex flex-col shadow-2xl backdrop-blur-sm"
        :style="dialogDynamicStyles"
      >
        <div class="p-6 border-b" :style="{ borderColor: 'var(--border-color)' }">
          <div class="flex justify-between items-center">
            <h2 class="text-2xl font-bold" :style="{ color: 'var(--text-color)' }">{{ modalTitle }}</h2>
            <button @click="closeModal" class="transition-opacity rounded-full p-1" :style="{ color: 'var(--text-color-medium)' }">
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg>
            </button>
          </div>
        </div>
        
        <div class="p-6 space-y-6 overflow-y-auto">
          <!-- 内容输入 -->
          <div>
            <label class="form-label">内容</label>
            <textarea v-model="noteForm.content" placeholder="分享你的想法..." class="form-textarea" maxlength="1000"></textarea>
            <div class="text-right text-xs mt-1" :style="{ color: 'var(--text-color-light)' }">{{ noteForm.content.length }}/1000</div>
          </div>

          <!-- 标签输入 -->
          <div>
            <label class="form-label">标签 (最多2个, 每个不超8字符)</label>
            <div class="flex flex-wrap gap-2 items-center">
              <span v-for="(tag, index) in noteForm.tags" :key="index" class="tag-item">
                {{ tag }}
                <button @click="removeTag(index)" class="ml-1 opacity-60 hover:opacity-100">×</button>
              </span>
              <input v-if="noteForm.tags.length < 2" v-model="newTag" @keyup.enter="addTag" @blur="addTag" placeholder="+ 添加" class="tag-input" maxlength="8">
            </div>
          </div>

          <!-- 背景色选择 -->
          <div>
            <label class="form-label">卡片颜色</label>
            <div class="flex flex-wrap gap-3">
              <button v-for="color in styleOptions" :key="color" @click="noteForm.style = color"
                class="w-6 h-6 border-2 transition-all duration-200 transform hover:scale-110"
                :class="noteForm.style === color ? 'scale-110' : ''"
                :style="{ backgroundColor: color, borderColor: noteForm.style === color ? 'var(--border-color-hover)' : 'var(--border-color)' }"
              >
                <span v-if="noteForm.style === color" :style="{ color: 'var(--text-color)' }">✓</span>
              </button>
            </div>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="p-6 mt-auto flex items-center gap-4">
          <div v-if="!props.note" class="flex-grow">
            <input type="text" v-model="noteForm.username" placeholder="署名 (未登录的记录可被用户编辑哦)" class="form-input w-full" maxlength="8">
          </div>
          <button @click="submitNote" class="submit-btn flex-shrink-0" :disabled="!noteForm.content.trim()">
            {{ modalSubmitText }}
          </button>
        </div>
      </div>
    </div>
  </Transition>
</template>

<style scoped>


.dialog-content {
  transition: background-color 0.3s ease-in-out;
}

.form-label {
  display: block;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-color-medium);
}

.form-textarea {
  width: 100%;
  padding: 0.75rem;
  resize: none;
  background-color: var(--bg-color-light);
  color: var(--text-color);
  min-height: 120px;
}
.form-textarea:focus {
  outline: none;
}
.form-textarea::placeholder {
  color: var(--text-color-light);
}

.form-input {
  width: 100%;
  padding: 0.5rem 0.75rem;
  background-color: var(--bg-color-light);
  color: var(--text-color);
}
.form-input:focus {
  outline: none;
}
.form-input::placeholder {
  color: var(--text-color-light);
}

.tag-item {
  display: inline-flex;
  align-items: center;
  font-size: 12px;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  background-color: var(--bg-color-light);
  border: 1px solid var(--border-color);
  color: var(--text-color-medium);
}

.tag-input {
  background-color: transparent;
  outline: none;
  text-align: center;
  transition-property: all;
  transition-duration: 200ms;
  border: 1px dashed var(--border-color);
  border-radius: 9999px;
  padding: 0.25rem 0.75rem;
  font-size: 12px;
  width: 80px;
  color: var(--text-color-light);
  cursor: text;
}
.tag-input::placeholder {
  color: var(--text-color-light);
}
.tag-input:focus {
  border-style: solid;
  border-color: var(--border-color-hover);
  color: var(--text-color);
}

.submit-btn {
  font-weight: 700;
  padding: 0.5rem 2rem;
  transition-property: all;
  transition-duration: 300ms;
  background-color: var(--bg-color-light);
  color: var(--text-color);
  cursor: pointer;
}

.submit-btn:hover{
  background-color: var(--bg-color-hover);
}


.submit-btn:hover:not(:disabled) {
  background-color: var(--bg-color-hover);
}
.submit-btn:disabled {
  opacity: 0.5;
  transform: none;
}

.modal-fade-enter-active,
.modal-fade-leave-active {
  transition: opacity 0.3s ease;
}
.modal-fade-enter-from,
.modal-fade-leave-to {
  opacity: 0;
}
.modal-fade-enter-active .dialog-content,
.modal-fade-leave-active .dialog-content {
  transition: transform 0.3s ease, background-color 0.3s ease-in-out;
}
.modal-fade-enter-from .dialog-content,
.modal-fade-leave-to .dialog-content {
  transform: scale(0.95) translateY(20px);
}
</style>