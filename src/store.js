import { createStore } from 'vuex'

// Create a new store instance.
const sortedTop = (sogaTop = [], update = false) => {
    let sortedKeyList = JSON.parse(localStorage.getItem('sortedKeyList'));
    if(update || !sortedKeyList || sortedKeyList.length === 0) {
        sortedKeyList = sogaTop.filter(item => { return !item.hide }).map(item => { return item.sourceKey })
        localStorage.setItem('sortedKeyList', JSON.stringify(sortedKeyList));
    }
    return sogaTop.map(item => {
        if(sortedKeyList.length) {
            let index = sortedKeyList.indexOf(item.sourceKey)
            if(index === -1) {
                item.hide = true
            }
        }

        return item
    }).sort((first, second) => {
        let firstIndex = sortedKeyList.indexOf(first.sourceKey)
        let secondIndex = sortedKeyList.indexOf(second.sourceKey)

        if(firstIndex === -1){
            return 1;
        }
        if(secondIndex === -1) {
            return -1;
        }
        if(firstIndex < secondIndex){
            return -1;
        }
        return 1;
    })
}

const setting = () => {
    const setting = localStorage.getItem('setting')
    if(setting) {
        return JSON.parse(setting)
    }
    return {
        hideTop10: false,
        safe: true
    }
}

const getStoredUser = () => {
    const userData = localStorage.getItem('user')
    if (userData) {
        try {
            return JSON.parse(userData)
        } catch (e) {
            return null
        }
    }
    return null
}

const getPopularLinkList = (top) => {
    const allLinks = top.reduce((links, source) => {
        if (source.data && Array.isArray(source.data)) {
            // 为每个链接添加来源信息
            let rank = 1;
            const sourceLinks = source.data.map(item => ({
                ...item,
                source: source.name,
                lastModifyTime: source.lastModifyTime,
                rank: rank++
            }));
            
            links.push(...sourceLinks);
        }
        return links;
    }, []);


    return {
        previewLinkList: allLinks.filter(link => !!link.preview && link.preview !== link.title).sort(() => Math.random() - 0.5).slice(0, 20),
        popularLinkList: allLinks.filter(link => link.rank <= 5).sort(() => Math.random() - 0.5).slice(0, 18),
    }
}


const store = {
    state () {
        return {
            completeMap: {},
            sogaTop: [],
            searchTop: [],
            linkList: [],
            popularLinkList: [],
            previewLinkList: [],
            language: 'en',
            scrolled: false,
            scrollDirection: 'none', // 'up', 'down', 'none'
            editable: false,
            lan: false,
            setting: setting(),
            user: getStoredUser()
        }
    },
    mutations: {
        ready (state, top) {
            state.sogaTop = sortedTop(top)
            const { popularLinkList, previewLinkList } = getPopularLinkList(top)
            state.popularLinkList = popularLinkList;
            state.previewLinkList = previewLinkList;
        },
        fetchLinkList(state, linkList) {
            state.linkList = linkList
        },
        fetchSearchTop(state, searchTop) {
            state.searchTop = searchTop;
        },
        sorted(state, top) {
            state.sogaTop = sortedTop(top, true);
        },
        complete(state, completeResult) {
            state.completeMap[completeResult.keyword] = completeResult.completeList
        },
        scrolling(state, scrolled) {
            state.scrolled = scrolled
        },
        updateScrollDirection(state, direction) {
            state.scrollDirection = direction
        },
        toggleEdit(state) {
            state.editable = !state.editable
        },
        saveSetting(state, target){
            state.setting[target['key']] = target['value']
            localStorage.setItem('setting', JSON.stringify(state.setting))
        },
        subscribe(state, sourceKey) {
            let target = state.sogaTop.find(item => item.sourceKey === sourceKey)
            target.hide = !target.hide
            state.sogaTop = sortedTop(state.sogaTop, true);
        },
        setLan(state, isLan) {
            state.lan = isLan
        },
        setUser(state, userData) {
            state.user = userData
            if (userData) {
                localStorage.setItem('user', JSON.stringify(userData))
            } else {
                localStorage.removeItem('user')
            }
        },
    },
    actions: {}
}

export default createStore(store)
