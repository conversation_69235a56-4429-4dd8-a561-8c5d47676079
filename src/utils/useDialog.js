import { reactive, h, createApp } from 'vue';
import AlertDialog from '../components/AlertDialog.vue';
import ConfirmDialog from '../components/ConfirmDialog.vue';
import Toast from '../components/Toast.vue';

const dialogState = reactive({
  alert: { visible: false, title: '', message: '', resolve: null },
  confirm: { visible: false, title: '', message: '', resolve: null },
  toast: { visible: false, message: '', large: false, timer: null },
});

const useDialog = () => {
  const alert = (title, message) => {
    return new Promise((resolve) => {
      dialogState.alert.title = title;
      dialogState.alert.message = message;
      dialogState.alert.resolve = resolve;
      dialogState.alert.visible = true;
    });
  };

  const confirm = (title, message) => {
    return new Promise((resolve) => {
      dialogState.confirm.title = title;
      dialogState.confirm.message = message;
      dialogState.confirm.resolve = resolve;
      dialogState.confirm.visible = true;
    });
  };

  const toast = (message, options = { large: false, duration: 2000 }) => {
    dialogState.toast.message = message;
    dialogState.toast.large = options.large || false;
    dialogState.toast.visible = true;

    if (dialogState.toast.timer) {
      clearTimeout(dialogState.toast.timer);
    }

    dialogState.toast.timer = setTimeout(() => {
      dialogState.toast.visible = false;
    }, options.duration || 2000);
  };

  return { alert, confirm, toast };
};

const DialogProvider = {
  setup() {
    const handleAlertClose = () => {
      dialogState.alert.visible = false;
      dialogState.alert.resolve(true);
    };

    const handleConfirm = () => {
      dialogState.confirm.visible = false;
      dialogState.confirm.resolve(true);
    };

    const handleCancel = () => {
      dialogState.confirm.visible = false;
      dialogState.confirm.resolve(false);
    };

    return () => [
      h(AlertDialog, {
        visible: dialogState.alert.visible,
        title: dialogState.alert.title,
        message: dialogState.alert.message,
        onClose: handleAlertClose,
      }),
      h(ConfirmDialog, {
        visible: dialogState.confirm.visible,
        title: dialogState.confirm.title,
        message: dialogState.confirm.message,
        onConfirm: handleConfirm,
        onCancel: handleCancel,
      }),
      h(Toast, {
        visible: dialogState.toast.visible,
        message: dialogState.toast.message,
        large: dialogState.toast.large,
      }),
    ];
  },
};

export const createDialog = (app) => {
  const dialogWrapper = document.createElement('div');
  document.body.appendChild(dialogWrapper);
  createApp(DialogProvider).mount(dialogWrapper);
};

export default useDialog;