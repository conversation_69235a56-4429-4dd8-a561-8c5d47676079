import axios from 'axios';
import { ref, reactive, provide, inject, readonly } from 'vue';
import { useRouter } from 'vue-router';

// 设置基础URL
// 在开发环境中，我们使用Vite的代理功能，所以baseURL可以是相对路径
// 在生产环境中，可以设置为空字符串（使用相对路径）或者设置为API的绝对URL
axios.defaults.baseURL = process.env.NODE_ENV === 'production' ? 'https://api.889990.xyz' : 'http://localhost:3000';

// 配置跨域请求携带凭证
axios.defaults.withCredentials = true;

//30s
axios.defaults.timeout = 30000


axios.interceptors.response.use(
  response => {
    return response.data;
  },
  error => {
    
    let errorCode = 503;
    let errorMessage = '';
    let errorStack = '';

    if (error.response) {
      errorCode = error.response.status;
      errorMessage = error.response.data.message;
      errorStack = error.stack || error.response.data.error || '';
    } else if (error.request) {
      errorMessage = '网络连接失败或服务器无响应';
    }

    console.error('Axios Error:', { errorCode, errorMessage, errorStack, originalError: error });
    // 抛出带有标准化错误信息的Promise.reject
    return Promise.reject({ errorCode, errorMessage, errorStack });
  }
);


export const HTTP_SERVICE_KEY = 'http_service';
export const HTTP_INSTANCE_KEY = 'http';

/**
 * 这是一个内部辅助函数，用于包装任何 Axios 调用，并添加页面状态处理逻辑。
 * @param {Function} axiosCallPromise 通常是 axiosService.get/post 等方法返回的 Promise
 * @param {Function} setPageError 用于设置页面错误状态的函数
 * @param {Function} setPageAvailable 用于设置页面可用状态的函数
 */
async function wrapAxiosCallWithPageStatus(axiosCallPromise, setPageError, setPageAvailable) {
  try {
    const responseData = await axiosCallPromise;
    setPageAvailable(); // 请求成功，设置页面为可用状态
    return responseData;
  } catch (error) {
    // 这里的 error 是从 axios 拦截器中抛出的 { errorCode, errorMessage, errorStack } 对象
    const { errorCode, errorMessage, errorStack } = error;
    setPageError(errorCode, errorMessage, errorStack); // 设置页面为错误状态
    throw error; // 重新抛出错误，以便组件可以进行特定于请求的额外处理（例如表单错误）
  }
}

/**
 * 创建一个增强的 Axios 实例，该实例包含用于处理主请求的 primary 方法。
 * @param {Object} originalAxiosService 原始的 axios 实例。
 * @param {Function} setPageError 用于设置页面错误状态的函数。
 * @param {Function} setPageAvailable 用于设置页面可用状态的函数。
 * @returns {Object} 增强的 Axios 实例，拥有原始 Axios 方法和 primary 方法。
 */
function createEnhancedAxiosInstance(originalAxiosService, setPageError, setPageAvailable) {
  // 创建一个空对象来承载增强的方法
  const enhancedService = {};

  // 1. 将原始 Axios 实例的所有方法（如 get, post, request 等）复制到增强实例上
  //    这些方法将按原样工作，不触发页面状态改变
  for (const method in originalAxiosService) {
    if (typeof originalAxiosService[method] === 'function') {
      enhancedService[method] = originalAxiosService[method];
    }
  }

  // 2. 添加 primary 方法，它返回一个包含包装过的 Axios 方法的对象
  enhancedService.primary = () => {
    const primaryMethods = {};
    const methodsToWrap = ['get', 'post', 'put', 'delete', 'patch']; // 你希望primary能调用的方法

    methodsToWrap.forEach(method => {
      if (typeof originalAxiosService[method] === 'function') {
        primaryMethods[method] = (...args) => {
          // 调用原始 Axios 方法，并将其 Promise 传入包装函数
          return wrapAxiosCallWithPageStatus(
            originalAxiosService[method](...args),
            setPageError,
            setPageAvailable
          );
        };
      }
    });
    return primaryMethods;
  };

  return enhancedService;
}

/**
 * 这是 Vue 插件对象，包含 install 方法。
 * main.js 将通过 app.use(http) 调用它。
 */
const httpPlugin = { // 内部仍然叫 httpPlugin，但导出时会以 http 命名
  install(app, options) {
    // 页面状态管理
    const pageStatus = reactive({
      available: true,
      errorCode: 200,
      errorMessage: '',
      errorStack: ''
    });

    const setPageAvailable = () => {
      pageStatus.available = true;
      pageStatus.errorCode = 200;
      pageStatus.errorMessage = '';
      pageStatus.errorStack = '';
    };

    const setPageError = (errorCode, errorMessage, errorStack = '') => {
      pageStatus.available = false;
      pageStatus.errorCode = errorCode;
      pageStatus.errorMessage = errorMessage;
      pageStatus.errorStack = errorStack;
      const { router }= useRouter()
      router.push('/not-available', { params: { errorCode, errorMessage, errorStack } });
    };

    // 创建增强的 Axios 实例，传递 pageStatus 的设置函数
    const enhancedAxios = createEnhancedAxiosInstance(axios, setPageError, setPageAvailable);

    // 将所有需要提供给组件的服务打包成一个对象
    const httpService = { // 更改变量名称
      status: readonly(pageStatus), // 提供只读的页面状态对象
      setPageAvailable,
      setPageError,
      http: enhancedAxios, // 提供增强后的 Axios 实例
    };

    // 通过 app.provide 将 httpService 对象提供给所有后代组件
    app.provide(HTTP_SERVICE_KEY, httpService);
    app.provide(HTTP_INSTANCE_KEY, enhancedAxios); 

  }
};

/**
 * 在子组件中调用此函数，注入全局应用服务。
 * @returns {Object} 包含页面状态和增强的 Axios 实例。
 */
export function useHttp() {
  const httpService = inject(HTTP_SERVICE_KEY);

  if (!httpService) {
    // 警告开发者，并在开发环境下提供一个哑巴（stub）实现
    console.error('useHttp must be used within a component that is a descendant of a component providing HTTP_SERVICE_KEY.');

    // 创建一个哑巴状态设置函数
    const dummySetPageAvailable = () => {};
    const dummySetPageError = () => {};
    // 创建一个哑巴增强 Axios 实例作为 fallback
    const dummyEnhancedAxios = createEnhancedAxiosInstance(axios, dummySetPageError, dummySetPageAvailable);

    return {
      status: readonly({
        available: true,
        errorCode: 200,
        errorMessage: '',
        errorStack: ''
      }),
      setPageAvailable: dummySetPageAvailable,
      setPageError: dummySetPageError,
      http: dummyEnhancedAxios, // 返回哑巴增强实例
    };
  }
  return httpService;
}


export default httpPlugin; // 导出时使用你想要的名称