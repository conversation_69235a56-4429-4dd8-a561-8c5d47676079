import axios from 'axios';
import { reactive, provide, inject, readonly } from 'vue';

// 设置基础URL
// 在开发环境中，我们使用Vite的代理功能，所以baseURL可以是相对路径
// 在生产环境中，可以设置为空字符串（使用相对路径）或者设置为API的绝对URL
axios.defaults.baseURL = process.env.NODE_ENV === 'production' ? 'https://api.889990.xyz' : 'http://localhost:3000';

// 配置跨域请求携带凭证
axios.defaults.withCredentials = true;

//30s
axios.defaults.timeout = 30000


axios.interceptors.response.use(
  response => {
    return response.data;
  },
  error => {
    
    let errorCode = 503;
    let errorMessage = '';
    let errorStack = '';

    if (error.response) {
      errorCode = error.response.status;
      errorMessage = error.response.data.message;
      errorStack = error.stack || error.response.data.error || '';
    } else if (error.request) {
      errorMessage = '网络连接失败或服务器无响应';
    }

    console.error('Axios Error:', { errorCode, errorMessage, errorStack, originalError: error });
    // 抛出带有标准化错误信息的Promise.reject
    return Promise.reject({ errorCode, errorMessage, errorStack });
  }
);

export const HTTP_SERVICE_KEY = 'http';

/**
 * 这是一个内部辅助函数，用于包装任何 Axios 调用，并添加页面状态处理逻辑。
 * @param {Function} axiosCallPromise 通常是 axiosService.get/post 等方法返回的 Promise
 * @param {Function} setPageError 用于设置页面错误状态的函数
 * @param {Function} setPageAvailable 用于设置页面可用状态的函数
 */
async function wrapAxiosCall(axiosCallPromise, setPageError, setPageAvailable) {
  try {
    const responseData = await axiosCallPromise;
    setPageAvailable(); // 请求成功，设置页面为可用状态
    return responseData;
  } catch (error) {

    // 这里的 error 是从 axios 拦截器中抛出的 { errorCode, errorMessage, errorStack } 对象
    const { errorCode, errorMessage, errorStack } = error;
    setPageError(errorCode, errorMessage, errorStack); // 设置页面为错误状态
    throw error; // 重新抛出错误，以便组件可以进行特定于请求的额外处理（例如表单错误）
  }
}

/**
 * 创建一个增强的 Axios 实例，该实例包含用于处理主请求的 primary 方法。
 * @returns {Object} 增强的 Axios 实例，拥有原始 Axios 方法和 primary 方法。
 */
function createAxiosInstance() {

    let httpService = inject(HTTP_SERVICE_KEY)

    if(httpService) {
        return httpService
    }

    // Instance-specific page status management
    const pageStatus = reactive({
        available: true,
        errorCode: 200,
        errorMessage: '',
        errorStack: ''
    });

    const setPageAvailable = () => {
        pageStatus.available = true;
        pageStatus.errorCode = 200;
        pageStatus.errorMessage = '';
        pageStatus.errorStack = '';
    };

    const setPageError = (errorCode, errorMessage, errorStack = '') => {
        pageStatus.available = false;
        pageStatus.errorCode = errorCode;
        pageStatus.errorMessage = errorMessage;
        pageStatus.errorStack = errorStack;
    };

    
    const primary = () => {
        const primaryMethods = {};
        const methodsToWrap = ['get', 'post', 'put', 'delete', 'patch']; // 你希望primary能调用的方法

        methodsToWrap.forEach(method => {
        if (typeof axios[method] === 'function') {
            primaryMethods[method] = (...args) => {
            // 调用原始 Axios 方法，并将其 Promise 传入包装函数
            return wrapAxiosCall(
                axios[method](...args),
                setPageError,
                setPageAvailable
            );
            };
        }
        });
        return primaryMethods;
    };


    // Return the service object for this component instance
    httpService = {
        status: readonly(pageStatus), // Provide a readonly version of the status
        setPageAvailable,
        setPageError,
        primary,
        defaults: axios.defaults
    };

    for (const method in axios) {
        if (typeof axios[method] === 'function') {
            httpService[method] = axios[method];
        }
    }

    provide(HTTP_SERVICE_KEY, httpService)

    return httpService;
}


/**
 * 在子组件中调用此函数，获取组件实例级别的 HTTP 服务和状态。
 * 每个调用都会创建一个独立的页面状态机。
 * @returns {Object} 包含页面状态和增强的 Axios 实例。
 */
export function useHttp() {
    return createAxiosInstance()
}
