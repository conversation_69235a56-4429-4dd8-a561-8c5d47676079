<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8"/>
    
    <!-- 基础SEO元标签 -->
    <title>999导航 - 专业的学术搜索与技术导航平台</title>
    <meta name="description" content="999导航是专为科研人员和技术开发者打造的导航平台，提供实时搜索、学术论文检索、技术资源导航等服务，助力学术研究与技术创新。"/>
    <meta name="keywords" content="999导航,学术搜索,科研导航,技术导航,论文检索,实时搜索,科研工具,开发者工具,学术资源"/>
    <meta name="author" content="999导航"/>
    <meta name="generator" content="Vue.js"/>
    <meta name="robots" content="index,follow"/>
    <meta name="googlebot" content="index,follow"/>
    <meta name="revisit-after" content="1 days"/>
    
    <!-- 规范链接 -->
    <link rel="canonical" href="https://889990.xyz/"/>
    
    <!-- Open Graph 标签 -->
    <meta property="og:type" content="website"/>
    <meta property="og:title" content="999导航 - 专业的学术搜索与技术导航平台"/>
    <meta property="og:description" content="999导航是专为科研人员和技术开发者打造的导航平台，提供实时搜索、学术论文检索、技术资源导航等服务。"/>
    <meta property="og:url" content="https://889990.xyz/"/>
    <meta property="og:image" content="https://889990.xyz/img/icon_180.png"/>
    <meta property="og:image:width" content="180"/>
    <meta property="og:image:height" content="180"/>
    <meta property="og:image:alt" content="999导航网站图标"/>
    <meta property="og:site_name" content="999导航"/>
    <meta property="og:locale" content="zh_CN"/>
    
    <!-- Twitter Card 标签 -->
    <meta name="twitter:card" content="summary"/>
    <meta name="twitter:title" content="999导航 - 专业的学术搜索与技术导航平台"/>
    <meta name="twitter:description" content="999导航是专为科研人员和技术开发者打造的导航平台，提供实时搜索、学术论文检索、技术资源导航等服务。"/>
    <meta name="twitter:image" content="https://889990.xyz/img/icon_180.png"/>
    <meta name="twitter:image:alt" content="999导航网站图标"/>
    
    <!-- 移动端和浏览器配置 -->
    <meta name="viewport" content="viewport-fit=cover,width=device-width,initial-scale=1,maximum-scale=1,user-scalable=no"/>
    <meta name="mobile-web-app-capable" content="yes"/>
    <meta name="apple-mobile-web-app-capable" content="yes"/>
    <meta name="apple-mobile-web-app-status-bar-style" content="default"/>
    <meta name="apple-mobile-web-app-title" content="999导航"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta name="format-detection" content="telephone=no"/>
    
    <!-- 图标配置 -->
    <link rel="icon" href="/img/favicon.ico"/>
    <link rel="apple-touch-icon" href="/img/icon_180.png" sizes="180x180"/>
    <link rel="mask-icon" href="/img/masted-icon.svg" color="#fff"/>
    <meta name="theme-color" content="#ffffff"/>
    <meta name="msapplication-TileColor" content="#ffffff"/>
    
    <!-- 站点地图 -->
    <link rel="sitemap" type="application/xml" href="/sitemap.xml"/>
    
    <!-- 结构化数据 (JSON-LD) -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "WebSite",
        "name": "999导航",
        "description": "专业的学术搜索与技术导航平台",
        "url": "https://889990.xyz/",
        "potentialAction": {
            "@type": "SearchAction",
            "target": "https://889990.xyz/search?q={search_term_string}",
            "query-input": "required name=search_term_string"
        },
        "publisher": {
            "@type": "Organization",
            "name": "999导航"
        }
    }
    </script>

    <!-- Google Analytics -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-G6LBY9XTEQ"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());
        gtag('config', 'G-G6LBY9XTEQ');
    </script>

    <!-- 加载动画样式 -->
    <style>
        .loader-container{background-color:#fff;position:fixed;top:0;bottom:0;left:0;right:0;z-index:999;}.loader{width:250px;height:50px;line-height:50px;text-align:center;position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);font-family:helvetica,arial,sans-serif;text-transform:uppercase;font-weight:900;color:#06c;letter-spacing:0.2em}.loader::before,.loader::after{content:"";display:block;width:15px;height:15px;background:#06c;position:absolute;animation:load .7s infinite alternate ease-in-out}.loader::before{top:0}.loader::after{bottom:0}@keyframes load{0%{left:0;height:30px;width:15px}50%{height:8px;width:40px}100%{left:235px;height:30px;width:15px}}
    </style>
</head>
<body>

<div id="app">
    <div class="loader-container">
        <div class="loader">
            999导航
        </div>
    </div>
</div>

<script type="module" src="/src/main.js"></script>

</body>
</html>
